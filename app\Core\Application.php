<?php

namespace App\Core;

use App\Core\Router;
use App\Core\Database;
use App\Core\Session;
use App\Core\Localization;
use App\Middleware\SecurityMiddleware;

/**
 * Основной класс приложения AstroGenix
 */
class Application
{
    private Router $router;
    private Database $database;
    private Session $session;
    private Localization $localization;
    private array $config;

    public function __construct()
    {
        $this->loadConfig();
        $this->initializeServices();
        $this->setupRoutes();
    }

    /**
     * Загрузка конфигурации
     */
    private function loadConfig(): void
    {
        $this->config = require CONFIG_PATH . '/app.php';
        
        // Установка временной зоны
        date_default_timezone_set($this->config['app']['timezone']);
    }

    /**
     * Инициализация сервисов
     */
    private function initializeServices(): void
    {
        // База данных
        $this->database = new Database($this->config['database']);
        
        // Сессии
        $this->session = new Session($this->config['security']);
        
        // Локализация
        $this->localization = new Localization($this->config['app']);
        
        // Роутер
        $this->router = new Router();
    }

    /**
     * Настройка маршрутов
     */
    private function setupRoutes(): void
    {
        // Главная страница
        $this->router->get('/', 'HomeController@index');
        
        // Аутентификация
        $this->router->get('/login', 'AuthController@showLogin');
        $this->router->post('/login', 'AuthController@login');
        $this->router->get('/register', 'AuthController@showRegister');
        $this->router->post('/register', 'AuthController@register');
        $this->router->post('/logout', 'AuthController@logout');
        
        // Панель пользователя
        $this->router->get('/dashboard', 'DashboardController@index');
        $this->router->get('/investments', 'InvestmentController@index');
        $this->router->post('/investments/create', 'InvestmentController@create');
        
        // Рефералы
        $this->router->get('/referrals', 'ReferralController@index');
        
        // Задания
        $this->router->get('/quests', 'QuestController@index');
        $this->router->post('/quests/complete', 'QuestController@complete');
        
        // Финансы
        $this->router->get('/deposit', 'DepositController@index');
        $this->router->post('/deposit/create', 'DepositController@create');
        $this->router->get('/withdrawal', 'WithdrawalController@index');
        $this->router->post('/withdrawal/create', 'WithdrawalController@create');
        
        // Новости
        $this->router->get('/news', 'NewsController@index');
        $this->router->get('/news/{id}', 'NewsController@show');
        
        // Поддержка
        $this->router->get('/support', 'SupportController@index');
        $this->router->post('/support/ticket', 'SupportController@createTicket');
        
        // API
        $this->router->get('/api/stats', 'ApiController@stats');
        $this->router->post('/api/locale', 'ApiController@setLocale');
        
        // Админ панель
        $this->router->get('/admin', 'AdminController@index');
        $this->router->get('/admin/users', 'AdminController@users');
        $this->router->get('/admin/deposits', 'AdminController@deposits');
        $this->router->get('/admin/withdrawals', 'AdminController@withdrawals');
        $this->router->get('/admin/settings', 'AdminController@settings');
    }

    /**
     * Запуск приложения
     */
    public function run(): void
    {
        try {
            // Применяем middleware безопасности
            $securityMiddleware = new SecurityMiddleware();
            $securityMiddleware->handle();
            
            // Обрабатываем запрос
            $this->router->dispatch();
            
        } catch (\Exception $e) {
            $this->handleException($e);
        }
    }

    /**
     * Обработка исключений
     */
    private function handleException(\Exception $e): void
    {
        // Логирование
        error_log("Application Error: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
        
        // В зависимости от типа запроса возвращаем ответ
        if ($this->isApiRequest()) {
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode([
                'error' => true,
                'message' => 'Internal server error'
            ]);
        } else {
            http_response_code(500);
            include APP_PATH . '/Views/errors/500.php';
        }
    }

    /**
     * Проверка, является ли запрос API
     */
    private function isApiRequest(): bool
    {
        return strpos($_SERVER['REQUEST_URI'], '/api/') === 0 || 
               (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
    }

    /**
     * Получение экземпляра базы данных
     */
    public function getDatabase(): Database
    {
        return $this->database;
    }

    /**
     * Получение конфигурации
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
