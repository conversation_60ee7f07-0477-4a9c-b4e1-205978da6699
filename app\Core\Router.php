<?php

namespace App\Core;

/**
 * Роутер для обработки HTTP запросов
 */
class Router
{
    private array $routes = [];
    private array $middleware = [];

    /**
     * Добавление GET маршрута
     */
    public function get(string $path, string $handler): void
    {
        $this->addRoute('GET', $path, $handler);
    }

    /**
     * Добавление POST маршрута
     */
    public function post(string $path, string $handler): void
    {
        $this->addRoute('POST', $path, $handler);
    }

    /**
     * Добавление PUT маршрута
     */
    public function put(string $path, string $handler): void
    {
        $this->addRoute('PUT', $path, $handler);
    }

    /**
     * Добавление DELETE маршрута
     */
    public function delete(string $path, string $handler): void
    {
        $this->addRoute('DELETE', $path, $handler);
    }

    /**
     * Добавление маршрута
     */
    private function addRoute(string $method, string $path, string $handler): void
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'pattern' => $this->convertToPattern($path)
        ];
    }

    /**
     * Преобразование пути в регулярное выражение
     */
    private function convertToPattern(string $path): string
    {
        // Заменяем параметры {id} на регулярные выражения
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        return '#^' . $pattern . '$#';
    }

    /**
     * Обработка запроса
     */
    public function dispatch(): void
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Удаляем trailing slash
        $path = rtrim($path, '/');
        if (empty($path)) {
            $path = '/';
        }

        foreach ($this->routes as $route) {
            if ($route['method'] === $method && preg_match($route['pattern'], $path, $matches)) {
                // Удаляем полное совпадение из массива
                array_shift($matches);
                
                $this->callHandler($route['handler'], $matches);
                return;
            }
        }

        // Маршрут не найден
        $this->handleNotFound();
    }

    /**
     * Вызов обработчика
     */
    private function callHandler(string $handler, array $params = []): void
    {
        [$controllerName, $methodName] = explode('@', $handler);
        
        $controllerClass = "App\\Controllers\\{$controllerName}";
        
        if (!class_exists($controllerClass)) {
            throw new \Exception("Controller {$controllerClass} not found");
        }

        $controller = new $controllerClass();
        
        if (!method_exists($controller, $methodName)) {
            throw new \Exception("Method {$methodName} not found in {$controllerClass}");
        }

        // Вызываем метод с параметрами
        call_user_func_array([$controller, $methodName], $params);
    }

    /**
     * Обработка 404 ошибки
     */
    private function handleNotFound(): void
    {
        http_response_code(404);
        
        if ($this->isApiRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => 'Route not found'
            ]);
        } else {
            include APP_PATH . '/Views/errors/404.php';
        }
    }

    /**
     * Проверка API запроса
     */
    private function isApiRequest(): bool
    {
        return strpos($_SERVER['REQUEST_URI'], '/api/') === 0 || 
               (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
    }

    /**
     * Добавление middleware
     */
    public function middleware(string $middleware): self
    {
        $this->middleware[] = $middleware;
        return $this;
    }

    /**
     * Генерация URL
     */
    public static function url(string $path = ''): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        return $protocol . '://' . $host . '/' . ltrim($path, '/');
    }

    /**
     * Редирект
     */
    public static function redirect(string $path, int $code = 302): void
    {
        header('Location: ' . self::url($path), true, $code);
        exit;
    }
}
