-- Создание таблицы пользователей
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    phone VARCHAR(20),
    country VARCHAR(2),
    referrer_id INT NULL,
    referral_code VARCHAR(20) UNIQUE NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    email_verified_at TIMESTAMP NULL,
    last_login_at TIMESTAMP NULL,
    last_activity_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referral_code (referral_code),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Создание таблицы балансов пользователей
CREATE TABLE user_balances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    balance_type ENUM('main', 'bonus', 'referral') DEFAULT 'main',
    amount DECIMAL(15,8) DEFAULT 0.00000000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_balance (user_id, balance_type),
    INDEX idx_user_id (user_id),
    INDEX idx_balance_type (balance_type),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Создание таблицы планов инвестиций
CREATE TABLE investment_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    daily_percent DECIMAL(5,2) NOT NULL,
    min_amount DECIMAL(15,8) NOT NULL,
    max_amount DECIMAL(15,8) NOT NULL,
    duration_days INT DEFAULT 365,
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
);

-- Вставка базовых планов инвестиций
INSERT INTO investment_plans (name, daily_percent, min_amount, max_amount, duration_days, sort_order) VALUES
('Starter', 1.00, 10.00000000, 100.00000000, 365, 1),
('Basic', 1.50, 101.00000000, 500.00000000, 365, 2),
('Advanced', 2.00, 501.00000000, 2000.00000000, 365, 3),
('Professional', 2.50, 2001.00000000, 10000.00000000, 365, 4);
