# 🔧 Устранение неполадок AstroGenix

## 🚨 Проблемы с установкой

### Проблема: Веб-установщик не работает

**Решения:**

1. **Используйте простой установщик:**
   ```
   http://yourdomain.com/setup.php
   ```

2. **Установка через командную строку:**
   ```bash
   php install_cli.php
   ```

3. **Проверьте права доступа:**
   ```bash
   chmod 755 storage/
   chmod 755 public/
   chmod 644 *.php
   ```

### Проблема: Ошибка подключения к базе данных

**Проверьте:**
- Правильность данных подключения
- Запущен ли MySQL сервер
- Существует ли пользователь базы данных
- Есть ли права на создание базы данных

**Решение:**
```sql
-- Создайте пользователя и базу данных вручную
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'astrogenix'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON astrogenix.* TO 'astrogenix'@'localhost';
FLUSH PRIVILEGES;
```

### Проблема: Ошибки PHP

**Проверьте версию PHP:**
```bash
php -v
```

**Установите необходимые расширения:**
```bash
# Ubuntu/Debian
sudo apt-get install php8.1-mysql php8.1-json php8.1-mbstring php8.1-openssl

# CentOS/RHEL
sudo yum install php81-php-mysqlnd php81-php-json php81-php-mbstring php81-php-openssl
```

## 🌐 Проблемы с веб-сервером

### Apache

**Проблема: 404 ошибки на всех страницах**

1. **Проверьте .htaccess:**
   ```apache
   # Убедитесь, что файл public/.htaccess существует
   ls -la public/.htaccess
   ```

2. **Включите mod_rewrite:**
   ```bash
   sudo a2enmod rewrite
   sudo systemctl restart apache2
   ```

3. **Проверьте конфигурацию виртуального хоста:**
   ```apache
   <VirtualHost *:80>
       DocumentRoot /path/to/astrogenix/public
       ServerName yourdomain.com
       
       <Directory /path/to/astrogenix/public>
           AllowOverride All
           Require all granted
       </Directory>
   </VirtualHost>
   ```

### Nginx

**Конфигурация для Nginx:**
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/astrogenix/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\. {
        deny all;
    }
}
```

## 🔐 Проблемы с безопасностью

### Проблема: CSRF ошибки

**Решение:**
1. Очистите кеш браузера
2. Проверьте, что JavaScript включен
3. Убедитесь, что сессии работают

### Проблема: Ошибки сессий

**Проверьте права на директорию сессий:**
```bash
# Найдите директорию сессий
php -r "echo session_save_path();"

# Установите права
sudo chmod 1733 /var/lib/php/sessions
```

## 📁 Проблемы с файлами

### Проблема: Ошибки записи файлов

**Установите правильные права:**
```bash
# Основные директории
chmod 755 app/ config/ database/ lang/ public/ vendor/
chmod 755 storage/ storage/logs/

# Файлы
chmod 644 *.php *.md *.json
chmod 644 public/*.php public/.htaccess

# Исполняемые файлы
chmod 755 install_cli.php
```

### Проблема: Загрузка файлов не работает

**Проверьте настройки PHP:**
```ini
; php.ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

## 🗄️ Проблемы с базой данных

### Проблема: Таблицы не создаются

**Выполните миграции вручную:**
```bash
mysql -u username -p database_name < database/migrations/001_create_users_table.sql
mysql -u username -p database_name < database/migrations/002_create_investments_table.sql
mysql -u username -p database_name < database/migrations/003_create_referral_system.sql
mysql -u username -p database_name < database/migrations/004_create_content_system.sql
```

### Проблема: Кодировка символов

**Проверьте настройки MySQL:**
```sql
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- Должно быть utf8mb4
```

## 🎨 Проблемы с дизайном

### Проблема: CSS/JS не загружаются

1. **Проверьте пути к файлам:**
   ```
   http://yourdomain.com/assets/css/app.css
   http://yourdomain.com/assets/js/app.js
   ```

2. **Очистите кеш браузера:** Ctrl+F5

3. **Проверьте права на файлы:**
   ```bash
   chmod 644 public/assets/css/*.css
   chmod 644 public/assets/js/*.js
   ```

### Проблема: Изображения не отображаются

**Проверьте:**
```bash
ls -la public/assets/images/
chmod 644 public/assets/images/*
```

## 🔄 Проблемы с функционалом

### Проблема: Не работает регистрация

1. **Проверьте логи:**
   ```bash
   tail -f storage/logs/security.log
   tail -f storage/logs/user_actions.log
   ```

2. **Проверьте настройки email** (если используется)

### Проблема: Не работают переводы

**Проверьте файлы локализации:**
```bash
ls -la lang/en/
ls -la lang/ru/
```

## 📊 Мониторинг и логи

### Просмотр логов

**Логи приложения:**
```bash
tail -f storage/logs/security.log
tail -f storage/logs/user_actions.log
```

**Логи веб-сервера:**
```bash
# Apache
tail -f /var/log/apache2/error.log
tail -f /var/log/apache2/access.log

# Nginx
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log
```

**Логи PHP:**
```bash
tail -f /var/log/php8.1-fpm.log
```

### Проверка производительности

**Мониторинг MySQL:**
```sql
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
```

**Мониторинг системы:**
```bash
top
htop
df -h
free -m
```

## 🆘 Экстренное восстановление

### Сброс пароля администратора

```sql
UPDATE users 
SET password = '$argon2id$v=19$m=65536,t=4,p=3$base64encodedstring' 
WHERE email = '<EMAIL>';
-- Пароль: admin123
```

### Переустановка

1. **Удалите файл блокировки:**
   ```bash
   rm storage/installed.lock
   ```

2. **Очистите базу данных:**
   ```sql
   DROP DATABASE astrogenix;
   ```

3. **Запустите установку заново**

## 📞 Получение помощи

1. **Проверьте логи** в `storage/logs/`
2. **Убедитесь в правильности конфигурации**
3. **Проверьте права доступа к файлам**
4. **Попробуйте переустановку**

---

**Если проблема не решена, соберите следующую информацию:**
- Версия PHP
- Версия MySQL
- Тип веб-сервера
- Сообщения об ошибках
- Содержимое логов
