<?php

namespace App\Models;

/**
 * Модель транзакций
 */
class Transaction extends BaseModel
{
    protected string $table = 'transactions';
    
    protected array $fillable = [
        'user_id', 'type', 'amount', 'balance_before', 'balance_after',
        'description', 'reference_id', 'reference_type', 'status'
    ];
    
    protected array $casts = [
        'user_id' => 'int',
        'amount' => 'float',
        'balance_before' => 'float',
        'balance_after' => 'float',
        'reference_id' => 'int'
    ];

    /**
     * Типы транзакций
     */
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_INVESTMENT = 'investment';
    const TYPE_PROFIT = 'profit';
    const TYPE_REFERRAL_BONUS = 'referral_bonus';
    const TYPE_QUEST_REWARD = 'quest_reward';
    const TYPE_WELCOME_BONUS = 'welcome_bonus';

    /**
     * Статусы транзакций
     */
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Создание транзакции
     */
    public function createTransaction(array $data): int
    {
        // Добавляем временную метку если её нет
        if (!isset($data['created_at'])) {
            $data['created_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->create($data);
    }

    /**
     * Получение транзакций пользователя
     */
    public function getUserTransactions(int $userId, int $limit = 50, int $offset = 0): array
    {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE user_id = :user_id
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        return $this->query($sql, [
            'user_id' => $userId,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    /**
     * Получение транзакций по типу
     */
    public function getTransactionsByType(int $userId, string $type, int $limit = 20): array
    {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE user_id = :user_id AND type = :type
            ORDER BY created_at DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, [
            'user_id' => $userId,
            'type' => $type,
            'limit' => $limit
        ]);
    }

    /**
     * Получение статистики транзакций пользователя
     */
    public function getUserTransactionStats(int $userId): array
    {
        $sql = "
            SELECT 
                type,
                COUNT(*) as count,
                SUM(amount) as total_amount,
                AVG(amount) as avg_amount,
                MAX(amount) as max_amount,
                MIN(amount) as min_amount
            FROM {$this->table}
            WHERE user_id = :user_id AND status = 'completed'
            GROUP BY type
        ";
        
        $results = $this->query($sql, ['user_id' => $userId]);
        
        $stats = [];
        foreach ($results as $result) {
            $stats[$result['type']] = [
                'count' => (int) $result['count'],
                'total_amount' => (float) $result['total_amount'],
                'avg_amount' => (float) $result['avg_amount'],
                'max_amount' => (float) $result['max_amount'],
                'min_amount' => (float) $result['min_amount']
            ];
        }
        
        return $stats;
    }

    /**
     * Получение общей статистики транзакций
     */
    public function getGlobalStats(): array
    {
        $sql = "
            SELECT 
                type,
                COUNT(*) as count,
                SUM(amount) as total_amount
            FROM {$this->table}
            WHERE status = 'completed'
            GROUP BY type
        ";
        
        $results = $this->query($sql);
        
        $stats = [
            'total_deposits' => 0,
            'total_withdrawals' => 0,
            'total_investments' => 0,
            'total_profits' => 0,
            'total_referral_bonuses' => 0,
            'total_quest_rewards' => 0
        ];
        
        foreach ($results as $result) {
            switch ($result['type']) {
                case self::TYPE_DEPOSIT:
                    $stats['total_deposits'] = (float) $result['total_amount'];
                    break;
                case self::TYPE_WITHDRAWAL:
                    $stats['total_withdrawals'] = (float) $result['total_amount'];
                    break;
                case self::TYPE_INVESTMENT:
                    $stats['total_investments'] = (float) $result['total_amount'];
                    break;
                case self::TYPE_PROFIT:
                    $stats['total_profits'] = (float) $result['total_amount'];
                    break;
                case self::TYPE_REFERRAL_BONUS:
                    $stats['total_referral_bonuses'] = (float) $result['total_amount'];
                    break;
                case self::TYPE_QUEST_REWARD:
                    $stats['total_quest_rewards'] = (float) $result['total_amount'];
                    break;
            }
        }
        
        return $stats;
    }

    /**
     * Получение транзакций за период
     */
    public function getTransactionsByPeriod(int $userId, string $startDate, string $endDate): array
    {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE user_id = :user_id 
            AND created_at >= :start_date 
            AND created_at <= :end_date
            ORDER BY created_at DESC
        ";
        
        return $this->query($sql, [
            'user_id' => $userId,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
    }

    /**
     * Получение ежедневной статистики транзакций
     */
    public function getDailyStats(int $userId, int $days = 30): array
    {
        $sql = "
            SELECT 
                DATE(created_at) as date,
                type,
                SUM(amount) as total_amount,
                COUNT(*) as count
            FROM {$this->table}
            WHERE user_id = :user_id 
            AND status = 'completed'
            AND created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY DATE(created_at), type
            ORDER BY date DESC
        ";
        
        return $this->query($sql, [
            'user_id' => $userId,
            'days' => $days
        ]);
    }

    /**
     * Получение последних транзакций
     */
    public function getRecentTransactions(int $limit = 10): array
    {
        $sql = "
            SELECT t.*, u.first_name, u.last_name, u.email
            FROM {$this->table} t
            JOIN users u ON t.user_id = u.id
            ORDER BY t.created_at DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, ['limit' => $limit]);
    }

    /**
     * Поиск транзакций
     */
    public function searchTransactions(array $filters, int $limit = 50, int $offset = 0): array
    {
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "user_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['type'])) {
            $whereConditions[] = "type = :type";
            $params['type'] = $filters['type'];
        }
        
        if (!empty($filters['status'])) {
            $whereConditions[] = "status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['start_date'])) {
            $whereConditions[] = "created_at >= :start_date";
            $params['start_date'] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $whereConditions[] = "created_at <= :end_date";
            $params['end_date'] = $filters['end_date'];
        }
        
        if (!empty($filters['min_amount'])) {
            $whereConditions[] = "amount >= :min_amount";
            $params['min_amount'] = $filters['min_amount'];
        }
        
        if (!empty($filters['max_amount'])) {
            $whereConditions[] = "amount <= :max_amount";
            $params['max_amount'] = $filters['max_amount'];
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        $sql = "
            SELECT t.*, u.first_name, u.last_name, u.email
            FROM {$this->table} t
            LEFT JOIN users u ON t.user_id = u.id
            {$whereClause}
            ORDER BY t.created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->query($sql, $params);
    }

    /**
     * Получение баланса на определенную дату
     */
    public function getBalanceAtDate(int $userId, string $date): float
    {
        $sql = "
            SELECT balance_after
            FROM {$this->table}
            WHERE user_id = :user_id 
            AND created_at <= :date
            AND status = 'completed'
            ORDER BY created_at DESC
            LIMIT 1
        ";
        
        $result = $this->db->fetch($sql, [
            'user_id' => $userId,
            'date' => $date
        ]);
        
        return $result ? (float) $result['balance_after'] : 0.0;
    }

    /**
     * Получение транзакций для экспорта
     */
    public function getTransactionsForExport(int $userId, string $startDate = null, string $endDate = null): array
    {
        $whereConditions = ["user_id = :user_id"];
        $params = ['user_id' => $userId];
        
        if ($startDate) {
            $whereConditions[] = "created_at >= :start_date";
            $params['start_date'] = $startDate;
        }
        
        if ($endDate) {
            $whereConditions[] = "created_at <= :end_date";
            $params['end_date'] = $endDate;
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        $sql = "
            SELECT 
                id,
                type,
                amount,
                description,
                status,
                created_at
            FROM {$this->table}
            WHERE {$whereClause}
            ORDER BY created_at ASC
        ";
        
        return $this->query($sql, $params);
    }

    /**
     * Получение суммы транзакций по типу за период
     */
    public function getSumByTypeAndPeriod(int $userId, string $type, string $startDate, string $endDate): float
    {
        $sql = "
            SELECT COALESCE(SUM(amount), 0) as total
            FROM {$this->table}
            WHERE user_id = :user_id 
            AND type = :type
            AND status = 'completed'
            AND created_at >= :start_date 
            AND created_at <= :end_date
        ";
        
        $result = $this->db->fetch($sql, [
            'user_id' => $userId,
            'type' => $type,
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
        
        return (float) $result['total'];
    }

    /**
     * Обновление статуса транзакции
     */
    public function updateStatus(int $transactionId, string $status): bool
    {
        return $this->update($transactionId, ['status' => $status]);
    }

    /**
     * Получение транзакций с пагинацией
     */
    public function getPaginatedTransactions(int $userId, int $page = 1, int $perPage = 20): array
    {
        $offset = ($page - 1) * $perPage;
        
        $sql = "
            SELECT * FROM {$this->table}
            WHERE user_id = :user_id
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $transactions = $this->query($sql, [
            'user_id' => $userId,
            'limit' => $perPage,
            'offset' => $offset
        ]);
        
        $total = $this->count(['user_id' => $userId]);
        
        return [
            'data' => $transactions,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }
}
