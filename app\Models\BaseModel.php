<?php

namespace App\Models;

use App\Core\Database;

/**
 * Базовая модель для всех моделей приложения
 */
abstract class BaseModel
{
    protected Database $db;
    protected string $table;
    protected string $primaryKey = 'id';
    protected array $fillable = [];
    protected array $hidden = [];
    protected array $casts = [];

    public function __construct()
    {
        $config = require CONFIG_PATH . '/app.php';
        $this->db = new Database($config['database']);
    }

    /**
     * Поиск записи по ID
     */
    public function find(int $id): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        $result = $this->db->fetch($sql, ['id' => $id]);
        
        return $result ? $this->processResult($result) : null;
    }

    /**
     * Поиск записи по условию
     */
    public function findBy(array $conditions): ?array
    {
        $whereClause = [];
        foreach (array_keys($conditions) as $column) {
            $whereClause[] = "{$column} = :{$column}";
        }
        
        $sql = "SELECT * FROM {$this->table} WHERE " . implode(' AND ', $whereClause) . " LIMIT 1";
        $result = $this->db->fetch($sql, $conditions);
        
        return $result ? $this->processResult($result) : null;
    }

    /**
     * Получение всех записей
     */
    public function all(array $conditions = [], string $orderBy = null, int $limit = null): array
    {
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach (array_keys($conditions) as $column) {
                $whereClause[] = "{$column} = :{$column}";
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $results = $this->db->fetchAll($sql, $conditions);
        
        return array_map([$this, 'processResult'], $results);
    }

    /**
     * Создание новой записи
     */
    public function create(array $data): int
    {
        $filteredData = $this->filterFillable($data);
        $filteredData = $this->applyCasts($filteredData);
        
        return $this->db->insert($this->table, $filteredData);
    }

    /**
     * Обновление записи
     */
    public function update(int $id, array $data): bool
    {
        $filteredData = $this->filterFillable($data);
        $filteredData = $this->applyCasts($filteredData);
        
        $rowsAffected = $this->db->update($this->table, $filteredData, [$this->primaryKey => $id]);
        
        return $rowsAffected > 0;
    }

    /**
     * Удаление записи
     */
    public function delete(int $id): bool
    {
        $rowsAffected = $this->db->delete($this->table, [$this->primaryKey => $id]);
        
        return $rowsAffected > 0;
    }

    /**
     * Подсчет записей
     */
    public function count(array $conditions = []): int
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach (array_keys($conditions) as $column) {
                $whereClause[] = "{$column} = :{$column}";
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        $result = $this->db->fetch($sql, $conditions);
        
        return (int) $result['count'];
    }

    /**
     * Пагинация
     */
    public function paginate(int $page = 1, int $perPage = 15, array $conditions = [], string $orderBy = null): array
    {
        $offset = ($page - 1) * $perPage;
        
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($conditions)) {
            $whereClause = [];
            foreach (array_keys($conditions) as $column) {
                $whereClause[] = "{$column} = :{$column}";
            }
            $sql .= " WHERE " . implode(' AND ', $whereClause);
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        $results = $this->db->fetchAll($sql, $conditions);
        $total = $this->count($conditions);
        
        return [
            'data' => array_map([$this, 'processResult'], $results),
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }

    /**
     * Фильтрация данных по fillable полям
     */
    protected function filterFillable(array $data): array
    {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }

    /**
     * Применение приведения типов
     */
    protected function applyCasts(array $data): array
    {
        foreach ($this->casts as $field => $type) {
            if (isset($data[$field])) {
                switch ($type) {
                    case 'int':
                    case 'integer':
                        $data[$field] = (int) $data[$field];
                        break;
                    case 'float':
                    case 'double':
                        $data[$field] = (float) $data[$field];
                        break;
                    case 'bool':
                    case 'boolean':
                        $data[$field] = (bool) $data[$field];
                        break;
                    case 'string':
                        $data[$field] = (string) $data[$field];
                        break;
                    case 'json':
                        $data[$field] = json_encode($data[$field]);
                        break;
                }
            }
        }
        
        return $data;
    }

    /**
     * Обработка результата запроса
     */
    protected function processResult(array $result): array
    {
        // Удаляем скрытые поля
        foreach ($this->hidden as $field) {
            unset($result[$field]);
        }
        
        // Применяем приведение типов для чтения
        foreach ($this->casts as $field => $type) {
            if (isset($result[$field])) {
                switch ($type) {
                    case 'int':
                    case 'integer':
                        $result[$field] = (int) $result[$field];
                        break;
                    case 'float':
                    case 'double':
                        $result[$field] = (float) $result[$field];
                        break;
                    case 'bool':
                    case 'boolean':
                        $result[$field] = (bool) $result[$field];
                        break;
                    case 'json':
                        $result[$field] = json_decode($result[$field], true);
                        break;
                }
            }
        }
        
        return $result;
    }

    /**
     * Выполнение произвольного SQL запроса
     */
    protected function query(string $sql, array $params = []): array
    {
        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Получение экземпляра базы данных
     */
    protected function getDatabase(): Database
    {
        return $this->db;
    }
}
