/**
 * AstroGenix - Main Application JavaScript
 */

class AstroGenix {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.hideLoadingScreen();
        this.initializeComponents();
        this.setupCSRF();
        this.setupAutoLogout();
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Auto-dismiss alerts
        document.querySelectorAll('[data-auto-dismiss]').forEach(alert => {
            const delay = parseInt(alert.dataset.autoDismiss);
            setTimeout(() => {
                alert.remove();
            }, delay);
        });

        // Form validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        });

        // Number formatting
        document.querySelectorAll('[data-format="currency"]').forEach(element => {
            this.formatCurrency(element);
        });

        // Copy to clipboard
        document.querySelectorAll('[data-copy]').forEach(element => {
            element.addEventListener('click', this.copyToClipboard.bind(this));
        });

        // Modal triggers
        document.querySelectorAll('[data-modal]').forEach(trigger => {
            trigger.addEventListener('click', this.openModal.bind(this));
        });

        // Dropdown toggles
        document.querySelectorAll('[data-dropdown]').forEach(dropdown => {
            dropdown.addEventListener('click', this.toggleDropdown.bind(this));
        });

        // Language switcher
        document.querySelectorAll('[data-locale]').forEach(switcher => {
            switcher.addEventListener('click', this.switchLocale.bind(this));
        });
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.remove();
                }, 500);
            }
        }, 1000);
    }

    /**
     * Initialize components
     */
    initializeComponents() {
        // Initialize charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            this.initializeCharts();
        }

        // Initialize tooltips
        this.initializeTooltips();

        // Initialize animations
        this.initializeAnimations();

        // Initialize real-time updates
        this.initializeRealTimeUpdates();
    }

    /**
     * Setup CSRF token for AJAX requests
     */
    setupCSRF() {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (token) {
            // Set default headers for fetch requests
            const originalFetch = window.fetch;
            window.fetch = function(url, options = {}) {
                options.headers = options.headers || {};
                options.headers['X-CSRF-TOKEN'] = token;
                return originalFetch(url, options);
            };

            // Set default headers for XMLHttpRequest
            const originalOpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
                originalOpen.call(this, method, url, async, user, password);
                this.setRequestHeader('X-CSRF-TOKEN', token);
            };
        }
    }

    /**
     * Setup auto logout for inactive users
     */
    setupAutoLogout() {
        if (!window.currentUser) return;

        let inactivityTimer;
        const inactivityTime = 30 * 60 * 1000; // 30 minutes

        const resetTimer = () => {
            clearTimeout(inactivityTimer);
            inactivityTimer = setTimeout(() => {
                this.showAlert('Session expired due to inactivity', 'warning');
                setTimeout(() => {
                    window.location.href = '/logout';
                }, 3000);
            }, inactivityTime);
        };

        // Reset timer on user activity
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, resetTimer, true);
        });

        resetTimer();
    }

    /**
     * Handle form submission
     */
    handleFormSubmit(event) {
        const form = event.target;
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading-spinner"></span> Loading...';
            
            // Re-enable button after 5 seconds to prevent permanent disable
            setTimeout(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = submitButton.dataset.originalText || 'Submit';
            }, 5000);
        }
    }

    /**
     * Format currency elements
     */
    formatCurrency(element) {
        const value = parseFloat(element.textContent);
        if (!isNaN(value)) {
            element.textContent = this.formatNumber(value, 2) + ' USDT';
        }
    }

    /**
     * Format number with locale
     */
    formatNumber(number, decimals = 2) {
        const locale = window.locale || 'en';
        return new Intl.NumberFormat(locale, {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    }

    /**
     * Copy text to clipboard
     */
    async copyToClipboard(event) {
        const element = event.target.closest('[data-copy]');
        const text = element.dataset.copy || element.textContent;
        
        try {
            await navigator.clipboard.writeText(text);
            this.showAlert('Copied to clipboard!', 'success');
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showAlert('Copied to clipboard!', 'success');
        }
    }

    /**
     * Open modal
     */
    openModal(event) {
        event.preventDefault();
        const trigger = event.target.closest('[data-modal]');
        const modalId = trigger.dataset.modal;
        
        // Load modal content via AJAX
        fetch(`/api/modal/${modalId}`)
            .then(response => response.text())
            .then(html => {
                const modalContainer = document.getElementById('modal-container');
                modalContainer.innerHTML = html;
                modalContainer.classList.add('active');
            })
            .catch(error => {
                console.error('Error loading modal:', error);
                this.showAlert('Error loading content', 'error');
            });
    }

    /**
     * Toggle dropdown
     */
    toggleDropdown(event) {
        event.preventDefault();
        const dropdown = event.target.closest('[data-dropdown]');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        // Close other dropdowns
        document.querySelectorAll('.dropdown-menu.active').forEach(otherMenu => {
            if (otherMenu !== menu) {
                otherMenu.classList.remove('active');
            }
        });
        
        menu.classList.toggle('active');
    }

    /**
     * Switch locale
     */
    switchLocale(event) {
        event.preventDefault();
        const locale = event.target.dataset.locale;
        
        fetch('/api/locale', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ locale })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error switching locale:', error);
        });
    }

    /**
     * Initialize charts
     */
    initializeCharts() {
        // Profit chart
        const profitChart = document.getElementById('profit-chart');
        if (profitChart) {
            this.createProfitChart(profitChart);
        }

        // Investment distribution chart
        const distributionChart = document.getElementById('distribution-chart');
        if (distributionChart) {
            this.createDistributionChart(distributionChart);
        }
    }

    /**
     * Create profit chart
     */
    createProfitChart(canvas) {
        new Chart(canvas, {
            type: 'line',
            data: {
                labels: [], // Will be populated with dates
                datasets: [{
                    label: 'Daily Profit',
                    data: [], // Will be populated with profit data
                    borderColor: '#00D4FF',
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: '#FFFFFF'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#B8BCC8' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#B8BCC8' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });
    }

    /**
     * Initialize tooltips
     */
    initializeTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }

    /**
     * Show tooltip
     */
    showTooltip(event) {
        const element = event.target;
        const text = element.dataset.tooltip;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        
        element._tooltip = tooltip;
    }

    /**
     * Hide tooltip
     */
    hideTooltip(event) {
        const element = event.target;
        if (element._tooltip) {
            element._tooltip.remove();
            delete element._tooltip;
        }
    }

    /**
     * Initialize animations
     */
    initializeAnimations() {
        // Intersection Observer for scroll animations
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        });

        document.querySelectorAll('[data-animate]').forEach(element => {
            observer.observe(element);
        });
    }

    /**
     * Initialize real-time updates
     */
    initializeRealTimeUpdates() {
        if (!window.currentUser) return;

        // Update balances every 30 seconds
        setInterval(() => {
            this.updateBalances();
        }, 30000);

        // Update mining animation
        this.startMiningAnimation();
    }

    /**
     * Update user balances
     */
    async updateBalances() {
        try {
            const response = await fetch('/api/user/balances');
            const data = await response.json();
            
            if (data.success) {
                // Update balance displays
                document.querySelectorAll('[data-balance]').forEach(element => {
                    const balanceType = element.dataset.balance;
                    if (data.balances[balanceType] !== undefined) {
                        element.textContent = this.formatNumber(data.balances[balanceType], 8);
                    }
                });
            }
        } catch (error) {
            console.error('Error updating balances:', error);
        }
    }

    /**
     * Start mining animation
     */
    startMiningAnimation() {
        const miningElements = document.querySelectorAll('.mining-animation');
        miningElements.forEach(element => {
            setInterval(() => {
                element.classList.add('pulse');
                setTimeout(() => {
                    element.classList.remove('pulse');
                }, 1000);
            }, 3000);
        });
    }

    /**
     * Show alert message
     */
    showAlert(message, type = 'info') {
        const alertsContainer = document.querySelector('.flash-messages') || this.createAlertsContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <div class="alert-content">
                <span class="alert-message">${message}</span>
            </div>
            <button class="alert-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        alertsContainer.appendChild(alert);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }

    /**
     * Create alerts container if it doesn't exist
     */
    createAlertsContainer() {
        const container = document.createElement('div');
        container.className = 'flash-messages';
        document.body.appendChild(container);
        return container;
    }

    /**
     * Make API request
     */
    async apiRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, mergedOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AstroGenix();
});

// Global utility functions
window.formatCurrency = (amount) => {
    return window.app.formatNumber(amount, 2) + ' USDT';
};

window.showAlert = (message, type) => {
    window.app.showAlert(message, type);
};
