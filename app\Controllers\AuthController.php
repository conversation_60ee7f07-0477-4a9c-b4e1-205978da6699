<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\UserBalance;

/**
 * Контроллер аутентификации
 */
class AuthController extends BaseController
{
    private User $userModel;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
    }

    /**
     * Показать форму входа
     */
    public function showLogin(): void
    {
        if ($this->session->isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        $this->render('auth.login');
    }

    /**
     * Обработка входа
     */
    public function login(): void
    {
        if ($this->session->isLoggedIn()) {
            $this->redirect('/dashboard');
        }

        $email = $this->input('email');
        $password = $this->input('password');
        $rememberMe = $this->input('remember_me', false);

        // Валидация
        $errors = $this->validate([
            'email' => $email,
            'password' => $password
        ], [
            'email' => 'required|email',
            'password' => 'required'
        ]);

        if (!empty($errors)) {
            if ($this->isApiRequest()) {
                $this->json(['errors' => $errors], 422);
            } else {
                $this->render('auth.login', ['errors' => $errors, 'email' => $email]);
                return;
            }
        }

        // Поиск пользователя
        $user = $this->userModel->findByEmail($email);
        
        if (!$user || !$this->userModel->verifyPassword($password, $user['password'])) {
            $error = 'Invalid email or password';
            
            if ($this->isApiRequest()) {
                $this->json(['error' => $error], 401);
            } else {
                $this->render('auth.login', ['error' => $error, 'email' => $email]);
                return;
            }
        }

        // Проверка статуса пользователя
        if ($user['status'] !== 'active') {
            $error = 'Your account is not active';
            
            if ($this->isApiRequest()) {
                $this->json(['error' => $error], 403);
            } else {
                $this->render('auth.login', ['error' => $error, 'email' => $email]);
                return;
            }
        }

        // Авторизация пользователя
        $this->session->setUser($user);
        $this->userModel->updateLastLogin($user['id']);

        // Логирование
        $this->logUserAction('login', ['ip' => $_SERVER['REMOTE_ADDR'] ?? '']);

        if ($this->isApiRequest()) {
            $this->json([
                'success' => true,
                'message' => 'Login successful',
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'first_name' => $user['first_name'],
                    'last_name' => $user['last_name']
                ]
            ]);
        } else {
            $this->redirect('/dashboard', ['success' => 'Welcome back!']);
        }
    }

    /**
     * Показать форму регистрации
     */
    public function showRegister(): void
    {
        if ($this->session->isLoggedIn()) {
            $this->redirect('/dashboard');
        }
        
        $referralCode = $this->input('ref');
        $this->render('auth.register', ['referralCode' => $referralCode]);
    }

    /**
     * Обработка регистрации
     */
    public function register(): void
    {
        if ($this->session->isLoggedIn()) {
            $this->redirect('/dashboard');
        }

        $data = [
            'first_name' => $this->input('first_name'),
            'last_name' => $this->input('last_name'),
            'email' => $this->input('email'),
            'password' => $this->input('password'),
            'password_confirmation' => $this->input('password_confirmation'),
            'referral_code' => $this->input('referral_code'),
            'terms_accepted' => $this->input('terms_accepted', false)
        ];

        // Валидация
        $errors = $this->validate($data, [
            'first_name' => 'required|min:2|max:50',
            'last_name' => 'required|min:2|max:50',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:8',
            'password_confirmation' => 'required'
        ]);

        // Проверка подтверждения пароля
        if ($data['password'] !== $data['password_confirmation']) {
            $errors['password_confirmation'] = 'Password confirmation does not match';
        }

        // Проверка принятия условий
        if (!$data['terms_accepted']) {
            $errors['terms_accepted'] = 'You must accept the terms and conditions';
        }

        if (!empty($errors)) {
            if ($this->isApiRequest()) {
                $this->json(['errors' => $errors], 422);
            } else {
                $this->render('auth.register', ['errors' => $errors, 'data' => $data]);
                return;
            }
        }

        // Проверка реферального кода
        $referrerId = null;
        if (!empty($data['referral_code'])) {
            $referrer = $this->userModel->findByReferralCode($data['referral_code']);
            if ($referrer) {
                $referrerId = $referrer['id'];
            }
        }

        // Создание пользователя
        try {
            $userId = $this->userModel->createUser([
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'],
                'password' => $data['password'],
                'referrer_id' => $referrerId,
                'country' => $this->detectUserCountry()
            ]);

            // Создание балансов пользователя
            $balanceModel = new UserBalance();
            $balanceModel->createUserBalances($userId);

            // Начисление приветственного бонуса
            $welcomeBonus = (float) $this->config['investment']['welcome_bonus'];
            if ($welcomeBonus > 0) {
                $balanceModel->addBalance($userId, $welcomeBonus, 'bonus');
                
                // Записываем транзакцию
                $transactionModel = new \App\Models\Transaction();
                $transactionModel->create([
                    'user_id' => $userId,
                    'type' => 'welcome_bonus',
                    'amount' => $welcomeBonus,
                    'balance_before' => 0,
                    'balance_after' => $welcomeBonus,
                    'description' => 'Welcome bonus'
                ]);
            }

            // Автоматический вход
            $user = $this->userModel->find($userId);
            $this->session->setUser($user);

            // Логирование
            $this->logUserAction('register', [
                'referrer_id' => $referrerId,
                'welcome_bonus' => $welcomeBonus
            ]);

            if ($this->isApiRequest()) {
                $this->json([
                    'success' => true,
                    'message' => 'Registration successful',
                    'user' => [
                        'id' => $user['id'],
                        'email' => $user['email'],
                        'first_name' => $user['first_name'],
                        'last_name' => $user['last_name']
                    ]
                ]);
            } else {
                $this->redirect('/dashboard', [
                    'success' => "Welcome to AstroGenix! You've received a {$welcomeBonus} USDT welcome bonus."
                ]);
            }

        } catch (\Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            
            if ($this->isApiRequest()) {
                $this->json(['error' => 'Registration failed'], 500);
            } else {
                $this->render('auth.register', [
                    'error' => 'Registration failed. Please try again.',
                    'data' => $data
                ]);
            }
        }
    }

    /**
     * Выход из системы
     */
    public function logout(): void
    {
        $this->logUserAction('logout');
        $this->session->logout();
        
        if ($this->isApiRequest()) {
            $this->json(['success' => true, 'message' => 'Logged out successfully']);
        } else {
            $this->redirect('/', ['success' => 'You have been logged out']);
        }
    }

    /**
     * Определение страны пользователя
     */
    private function detectUserCountry(): string
    {
        // Упрощенное определение по языку браузера
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        
        if (strpos($acceptLanguage, 'ru') !== false) {
            return 'RU';
        }
        
        return 'US';
    }
}
