# AstroGenix - Premium USDT Staking Platform

AstroGenix - это полнофункциональная платформа для USDT стейкинга с современным дизайном, комплексной системой инвестиций, реферальной программой и системой заданий.

## 🚀 Особенности

### 💰 Система инвестиций
- 4 инвестиционных плана с доходностью от 1% до 2.5% в день
- Автоматическое начисление ежедневной прибыли
- Визуализация процесса как "майнинг" с анимациями
- Приветственный бонус при регистрации

### 👥 Реферальная программа
- 3-уровневая система (7%, 3%, 1%)
- Бонусы за приглашение определенного количества рефералов
- Детальная статистика и аналитика

### 🎯 Система заданий (квестов)
- Ежедневный вход (streak система)
- Недельная активность
- Приглашение рефералов
- Первое пополнение
- Подписка на социальные сети

### 💳 Финансовая система
- Ручная система пополнения с подтверждением администратора
- Ручная система вывода с одобрением
- История всех транзакций
- Система тикетов поддержки

### 📰 Контент-система
- Система публикации новостей
- Категории и теги
- SEO-оптимизация

### 🌐 Локализация
- Поддержка русского и английского языков
- Автоматическое определение языка по IP
- Функция t() для всех текстовых элементов

### 🎨 Дизайн
- Премиум крипто-платформа в стиле Binance/Coinbase
- Темная тема с неоновыми акцентами
- Glassmorphism эффекты
- Mobile-first подход
- Плавные анимации и glow-эффекты

### 🔐 Безопасность
- Защита от CSRF, XSS, SQL-инъекций
- Ограничение частоты запросов
- Логирование всех действий
- Безопасные заголовки HTTP

## 📋 Требования

- PHP 8.1+
- MySQL 8.0+
- Web-сервер (Apache/Nginx)
- Расширения PHP: PDO, JSON, mbstring, OpenSSL

## 🛠 Установка

### 1. Клонирование проекта
```bash
git clone <repository-url> astrogenix
cd astrogenix
```

### 2. Настройка окружения
```bash
cp .env.example .env
```

Отредактируйте файл `.env` и укажите настройки базы данных:
```env
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=astrogenix
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 3. Создание базы данных
Создайте базу данных MySQL:
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. Инициализация базы данных
```bash
php database/init.php
```

Этот скрипт:
- Выполнит все миграции
- Создаст администратора по умолчанию
- Настроит базовые данные

### 5. Настройка веб-сервера

#### Apache
Убедитесь, что DocumentRoot указывает на папку `public/`:
```apache
<VirtualHost *:80>
    DocumentRoot /path/to/astrogenix/public
    ServerName astrogenix.local
    
    <Directory /path/to/astrogenix/public>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name astrogenix.local;
    root /path/to/astrogenix/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 6. Настройка прав доступа
```bash
chmod -R 755 storage/
chmod -R 755 public/uploads/
```

## 🔑 Доступ по умолчанию

После установки вы можете войти в систему:

**Администратор:**
- Email: `<EMAIL>`
- Пароль: `admin123`

⚠️ **Важно:** Обязательно смените пароль администратора после первого входа!

## 📁 Структура проекта

```
astrogenix/
├── app/
│   ├── Controllers/     # Контроллеры
│   ├── Models/         # Модели данных
│   ├── Views/          # Представления
│   ├── Middleware/     # Промежуточное ПО
│   ├── Services/       # Сервисы
│   └── Core/           # Ядро приложения
├── config/             # Конфигурационные файлы
├── database/
│   ├── migrations/     # Миграции базы данных
│   └── seeds/          # Начальные данные
├── lang/               # Файлы локализации
├── public/             # Публичные файлы
│   ├── assets/         # CSS, JS, изображения
│   └── index.php       # Точка входа
├── storage/
│   └── logs/           # Логи приложения
└── vendor/             # Автозагрузчик
```

## ⚙️ Конфигурация

### Основные настройки
Все настройки хранятся в базе данных в таблице `site_settings`. Основные параметры:

- `welcome_bonus` - Приветственный бонус (по умолчанию: 10 USDT)
- `min_withdrawal` - Минимальная сумма вывода (по умолчанию: 5 USDT)
- `withdrawal_fee` - Комиссия за вывод в % (по умолчанию: 2%)
- `referral_level_1` - Процент с 1-го уровня рефералов (по умолчанию: 7%)
- `referral_level_2` - Процент со 2-го уровня рефералов (по умолчанию: 3%)
- `referral_level_3` - Процент с 3-го уровня рефералов (по умолчанию: 1%)

### Планы инвестиций
Настраиваются в файле `config/app.php` в разделе `investment.plans`:

```php
'plans' => [
    1 => ['name' => 'Starter', 'daily_percent' => 1.0, 'min_amount' => 10, 'max_amount' => 100],
    2 => ['name' => 'Basic', 'daily_percent' => 1.5, 'min_amount' => 101, 'max_amount' => 500],
    3 => ['name' => 'Advanced', 'daily_percent' => 2.0, 'min_amount' => 501, 'max_amount' => 2000],
    4 => ['name' => 'Professional', 'daily_percent' => 2.5, 'min_amount' => 2001, 'max_amount' => 10000],
]
```

## 🔄 Автоматические процессы

### Начисление прибыли
Создайте cron-задачу для автоматического начисления ежедневной прибыли:

```bash
# Добавьте в crontab (crontab -e)
0 0 * * * php /path/to/astrogenix/scripts/daily_profit.php
```

### Очистка логов
```bash
# Очистка старых логов (раз в неделю)
0 0 * * 0 find /path/to/astrogenix/storage/logs -name "*.log" -mtime +30 -delete
```

## 🛡️ Безопасность

### Рекомендации по безопасности:
1. Используйте HTTPS в продакшене
2. Регулярно обновляйте пароли
3. Настройте файрвол
4. Ограничьте доступ к административным файлам
5. Регулярно создавайте резервные копии базы данных

### Настройка SSL (Let's Encrypt):
```bash
certbot --nginx -d yourdomain.com
```

## 📊 Мониторинг

### Логи приложения
- `storage/logs/security.log` - События безопасности
- `storage/logs/user_actions.log` - Действия пользователей
- `storage/logs/admin_actions.log` - Действия администраторов

### Мониторинг производительности
Рекомендуется настроить мониторинг:
- Использование CPU и памяти
- Время отклика базы данных
- Количество активных пользователей

## 🔧 Разработка

### Добавление новых функций
1. Создайте миграцию в `database/migrations/`
2. Добавьте модель в `app/Models/`
3. Создайте контроллер в `app/Controllers/`
4. Добавьте маршруты в `app/Core/Application.php`
5. Создайте представления в `app/Views/`

### Локализация
Добавьте переводы в файлы:
- `lang/en/` - английские переводы
- `lang/ru/` - русские переводы

## 📞 Поддержка

Для получения поддержки:
1. Проверьте логи в `storage/logs/`
2. Убедитесь в правильности конфигурации
3. Проверьте права доступа к файлам

## 📄 Лицензия

Этот проект создан для демонстрационных целей. Используйте на свой страх и риск.

---

**AstroGenix** - Премиум платформа для USDT стейкинга 🚀
