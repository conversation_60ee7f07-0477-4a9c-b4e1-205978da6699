<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D4FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Outer Ring -->
  <circle cx="40" cy="40" r="38" stroke="url(#logoGradient)" stroke-width="2" fill="none" opacity="0.3"/>
  
  <!-- Middle Ring -->
  <circle cx="40" cy="40" r="30" stroke="url(#logoGradient)" stroke-width="1.5" fill="none" opacity="0.5"/>
  
  <!-- Inner Core -->
  <circle cx="40" cy="40" r="20" fill="url(#logoGradient)" filter="url(#glow)"/>
  
  <!-- Central Star -->
  <g transform="translate(40,40)">
    <path d="M0,-12 L3,-3 L12,0 L3,3 L0,12 L-3,3 L-12,0 L-3,-3 Z" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Orbiting Particles -->
  <g transform="translate(40,40)">
    <circle cx="25" cy="0" r="2" fill="#00FF88" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-25" cy="0" r="2" fill="#00D4FF" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0;-360" dur="6s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="25" r="1.5" fill="#8B5CF6" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="-25" r="1.5" fill="#00FF88" opacity="0.8">
      <animateTransform attributeName="transform" type="rotate" values="0;-360" dur="7s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Pulsing Effect -->
  <circle cx="40" cy="40" r="15" fill="url(#logoGradient)" opacity="0.2">
    <animate attributeName="r" values="15;25;15" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0;0.2" dur="2s" repeatCount="indefinite"/>
  </circle>
</svg>
