<div class="auth-container">
    <div class="auth-wrapper">
        <div class="auth-card glass-card">
            <!-- Logo -->
            <div class="auth-logo">
                <img src="/assets/images/logo.svg" alt="AstroGenix" class="logo-image">
                <h1 class="logo-text">AstroGenix</h1>
                <p class="logo-subtitle"><?= $t('auth.welcome_back') ?></p>
            </div>

            <!-- Login Form -->
            <form method="POST" action="/login" class="auth-form">
                <input type="hidden" name="csrf_token" value="<?= $csrfToken ?>">
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <div class="alert-content">
                            <span class="alert-message"><?= htmlspecialchars($error) ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="form-group">
                    <label for="email" class="form-label">
                        <svg class="form-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        <?= $t('common.email') ?>
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input <?= isset($errors['email']) ? 'error' : '' ?>"
                        value="<?= htmlspecialchars($email ?? '') ?>"
                        placeholder="<?= $t('auth.enter_email') ?>"
                        required
                        autocomplete="email"
                    >
                    <?php if (isset($errors['email'])): ?>
                        <div class="form-error"><?= htmlspecialchars($errors['email']) ?></div>
                    <?php endif; ?>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <svg class="form-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                        <?= $t('common.password') ?>
                    </label>
                    <div class="password-input-wrapper">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input <?= isset($errors['password']) ? 'error' : '' ?>"
                            placeholder="<?= $t('auth.enter_password') ?>"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <svg class="eye-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                    <?php if (isset($errors['password'])): ?>
                        <div class="form-error"><?= htmlspecialchars($errors['password']) ?></div>
                    <?php endif; ?>
                </div>

                <div class="form-group form-options">
                    <label class="checkbox-label">
                        <input type="checkbox" name="remember_me" value="1" class="checkbox-input">
                        <span class="checkbox-custom"></span>
                        <span class="checkbox-text"><?= $t('common.remember_me') ?></span>
                    </label>
                    
                    <a href="/forgot-password" class="forgot-link">
                        <?= $t('common.forgot_password') ?>
                    </a>
                </div>

                <button type="submit" class="btn btn-primary btn-lg w-full" data-original-text="<?= $t('common.login') ?>">
                    <svg class="btn-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <?= $t('common.login') ?>
                </button>
            </form>

            <!-- Register Link -->
            <div class="auth-footer">
                <p class="auth-footer-text">
                    <?= $t('auth.no_account') ?>
                    <a href="/register<?= isset($_GET['ref']) ? '?ref=' . htmlspecialchars($_GET['ref']) : '' ?>" class="auth-link">
                        <?= $t('common.register') ?>
                    </a>
                </p>
            </div>

            <!-- Social Login (if implemented) -->
            <div class="social-login">
                <div class="social-divider">
                    <span><?= $t('auth.or_continue_with') ?></span>
                </div>
                
                <!-- Add social login buttons here if needed -->
            </div>
        </div>

        <!-- Features Sidebar -->
        <div class="auth-features">
            <div class="feature-item">
                <div class="feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="feature-content">
                    <h3><?= $t('features.daily_profits') ?></h3>
                    <p><?= $t('features.daily_profits_desc') ?></p>
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="feature-content">
                    <h3><?= $t('features.secure_platform') ?></h3>
                    <p><?= $t('features.secure_platform_desc') ?></p>
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div class="feature-content">
                    <h3><?= $t('features.referral_program') ?></h3>
                    <p><?= $t('features.referral_program_desc') ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
    background: 
        radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%);
}

.auth-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    max-width: 1200px;
    width: 100%;
}

.auth-card {
    max-width: 400px;
    margin: 0 auto;
}

.auth-logo {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.logo-image {
    width: 60px;
    height: 60px;
    margin-bottom: var(--spacing-md);
}

.logo-text {
    font-size: 2rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: var(--spacing-sm);
}

.logo-subtitle {
    color: var(--text-secondary);
    margin-bottom: 0;
}

.form-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.form-icon {
    width: 16px;
    height: 16px;
    color: var(--primary-blue);
}

.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.eye-icon {
    width: 16px;
    height: 16px;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.checkbox-input {
    display: none;
}

.checkbox-custom {
    width: 16px;
    height: 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
}

.checkbox-input:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-blue);
    font-size: 12px;
}

.forgot-link {
    font-size: 0.875rem;
    color: var(--primary-blue);
}

.btn-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-sm);
}

.auth-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.auth-footer-text {
    color: var(--text-secondary);
    margin-bottom: 0;
}

.auth-link {
    color: var(--primary-blue);
    font-weight: 500;
}

.social-login {
    margin-top: var(--spacing-lg);
}

.social-divider {
    text-align: center;
    position: relative;
    margin: var(--spacing-lg) 0;
}

.social-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.social-divider span {
    background: var(--dark-surface);
    padding: 0 var(--spacing-md);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.auth-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
}

.feature-item {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.feature-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon svg {
    width: 20px;
    height: 20px;
    color: white;
}

.feature-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.feature-content p {
    color: var(--text-secondary);
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .auth-wrapper {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .auth-features {
        order: -1;
        padding: var(--spacing-lg);
    }
    
    .feature-item {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}
</script>
