<?php

namespace App\Controllers;

use App\Core\Session;
use App\Core\Localization;

/**
 * Базовый контроллер для всех контроллеров приложения
 */
abstract class BaseController
{
    protected Session $session;
    protected Localization $localization;
    protected array $config;
    protected ?array $currentUser = null;

    public function __construct()
    {
        $this->config = require CONFIG_PATH . '/app.php';
        $this->session = new Session($this->config['security']);
        $this->localization = new Localization($this->config['app']);
        
        $this->loadCurrentUser();
    }

    /**
     * Загрузка текущего пользователя
     */
    protected function loadCurrentUser(): void
    {
        if ($this->session->isLoggedIn()) {
            $userModel = new \App\Models\User();
            $this->currentUser = $userModel->find($this->session->getUserId());
        }
    }

    /**
     * Рендеринг представления
     */
    protected function render(string $view, array $data = []): void
    {
        // Добавляем глобальные переменные
        $data['currentUser'] = $this->currentUser;
        $data['session'] = $this->session;
        $data['config'] = $this->config;
        $data['t'] = [$this->localization, 'get'];
        $data['locale'] = $this->localization->getCurrentLocale();
        $data['csrfToken'] = $this->session->generateCsrfToken();
        
        // Добавляем flash сообщения
        $data['flashMessages'] = $this->session->getAllFlash();
        
        $viewPath = $this->config['paths']['views'] . '/' . str_replace('.', '/', $view) . '.php';
        
        if (!file_exists($viewPath)) {
            throw new \Exception("View {$view} not found");
        }
        
        // Извлекаем переменные в локальную область видимости
        extract($data);
        
        // Подключаем layout
        include $this->config['paths']['views'] . '/layouts/app.php';
    }

    /**
     * Рендеринг JSON ответа
     */
    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Редирект
     */
    protected function redirect(string $path, array $flashData = []): void
    {
        // Устанавливаем flash сообщения
        foreach ($flashData as $type => $message) {
            $this->session->flash($type, $message);
        }
        
        header('Location: ' . $this->url($path));
        exit;
    }

    /**
     * Генерация URL
     */
    protected function url(string $path = ''): string
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        return $protocol . '://' . $host . '/' . ltrim($path, '/');
    }

    /**
     * Проверка авторизации
     */
    protected function requireAuth(): void
    {
        if (!$this->session->isLoggedIn()) {
            if ($this->isApiRequest()) {
                $this->json(['error' => 'Unauthorized'], 401);
            } else {
                $this->redirect('/login', ['error' => 'Please login to continue']);
            }
        }
    }

    /**
     * Проверка прав администратора
     */
    protected function requireAdmin(): void
    {
        $this->requireAuth();
        
        if (!$this->session->isAdmin()) {
            if ($this->isApiRequest()) {
                $this->json(['error' => 'Access denied'], 403);
            } else {
                $this->redirect('/dashboard', ['error' => 'Access denied']);
            }
        }
    }

    /**
     * Проверка API запроса
     */
    protected function isApiRequest(): bool
    {
        return strpos($_SERVER['REQUEST_URI'], '/api/') === 0 || 
               (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
    }

    /**
     * Валидация входных данных
     */
    protected function validate(array $data, array $rules): array
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = explode('|', $rule);
            
            foreach ($ruleList as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule, $data);
                if ($error) {
                    $errors[$field] = $error;
                    break; // Останавливаемся на первой ошибке для поля
                }
            }
        }
        
        return $errors;
    }

    /**
     * Валидация отдельного поля
     */
    private function validateField(string $field, $value, string $rule, array $data): ?string
    {
        $ruleParts = explode(':', $rule);
        $ruleName = $ruleParts[0];
        $ruleValue = $ruleParts[1] ?? null;
        
        switch ($ruleName) {
            case 'required':
                if (empty($value)) {
                    return "Field {$field} is required";
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "Field {$field} must be a valid email";
                }
                break;
                
            case 'min':
                if (!empty($value) && strlen($value) < (int)$ruleValue) {
                    return "Field {$field} must be at least {$ruleValue} characters";
                }
                break;
                
            case 'max':
                if (!empty($value) && strlen($value) > (int)$ruleValue) {
                    return "Field {$field} must not exceed {$ruleValue} characters";
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    return "Field {$field} must be numeric";
                }
                break;
                
            case 'confirmed':
                $confirmField = $field . '_confirmation';
                if ($value !== ($data[$confirmField] ?? null)) {
                    return "Field {$field} confirmation does not match";
                }
                break;
                
            case 'unique':
                // Для проверки уникальности нужна модель
                $tableParts = explode(',', $ruleValue);
                $table = $tableParts[0];
                $column = $tableParts[1] ?? $field;
                
                if (!empty($value)) {
                    $db = new \App\Core\Database($this->config['database']);
                    $existing = $db->fetch("SELECT id FROM {$table} WHERE {$column} = :value", ['value' => $value]);
                    if ($existing) {
                        return "Field {$field} must be unique";
                    }
                }
                break;
        }
        
        return null;
    }

    /**
     * Получение входных данных
     */
    protected function input(string $key = null, $default = null)
    {
        $input = array_merge($_GET, $_POST);
        
        if ($key === null) {
            return $input;
        }
        
        return $input[$key] ?? $default;
    }

    /**
     * Логирование действий пользователя
     */
    protected function logUserAction(string $action, array $data = []): void
    {
        if (!$this->currentUser) {
            return;
        }
        
        $logData = [
            'user_id' => $this->currentUser['id'],
            'action' => $action,
            'data' => json_encode($data),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $logFile = ROOT_PATH . '/storage/logs/user_actions.log';
        $logEntry = json_encode($logData) . "\n";
        
        // Создаем директорию если её нет
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Форматирование суммы
     */
    protected function formatAmount(float $amount): string
    {
        return $this->localization->formatCurrency($amount);
    }

    /**
     * Форматирование даты
     */
    protected function formatDate(string $date): string
    {
        return $this->localization->formatDate(new \DateTime($date));
    }
}
