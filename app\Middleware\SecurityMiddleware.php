<?php

namespace App\Middleware;

/**
 * Middleware для обеспечения безопасности
 */
class SecurityMiddleware
{
    /**
     * Обработка запроса
     */
    public function handle(): void
    {
        $this->setSecurityHeaders();
        $this->validateCsrfToken();
        $this->preventXSS();
        $this->rateLimiting();
    }

    /**
     * Установка заголовков безопасности
     */
    private function setSecurityHeaders(): void
    {
        // Защита от XSS
        header('X-XSS-Protection: 1; mode=block');
        
        // Защита от MIME-type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Защита от clickjacking
        header('X-Frame-Options: DENY');
        
        // Строгая транспортная безопасность (только для HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
        
        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; " .
               "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; " .
               "font-src 'self' https://fonts.gstatic.com; " .
               "img-src 'self' data: https:; " .
               "connect-src 'self'; " .
               "frame-src 'none';";
        
        header("Content-Security-Policy: {$csp}");
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }

    /**
     * Проверка CSRF токена для POST запросов
     */
    private function validateCsrfToken(): void
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
            
            if (empty($token)) {
                $this->respondWithError('CSRF token missing', 403);
                return;
            }
            
            session_start();
            
            if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $token)) {
                $this->respondWithError('Invalid CSRF token', 403);
                return;
            }
            
            // Проверяем время жизни токена
            $tokenTime = $_SESSION['csrf_token_time'] ?? 0;
            if (time() - $tokenTime > 3600) { // 1 час
                $this->respondWithError('CSRF token expired', 403);
                return;
            }
        }
    }

    /**
     * Защита от XSS атак
     */
    private function preventXSS(): void
    {
        // Очистка GET параметров
        foreach ($_GET as $key => $value) {
            $_GET[$key] = $this->sanitizeInput($value);
        }
        
        // Очистка POST параметров (кроме специальных полей)
        $skipFields = ['content', 'message', 'description']; // Поля, которые могут содержать HTML
        
        foreach ($_POST as $key => $value) {
            if (!in_array($key, $skipFields)) {
                $_POST[$key] = $this->sanitizeInput($value);
            }
        }
    }

    /**
     * Очистка входных данных
     */
    private function sanitizeInput($input)
    {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        // Удаляем потенциально опасные символы
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        return $input;
    }

    /**
     * Ограничение частоты запросов
     */
    private function rateLimiting(): void
    {
        $ip = $this->getClientIP();
        $key = "rate_limit_{$ip}";
        
        session_start();
        
        $now = time();
        $windowSize = 60; // 1 минута
        $maxRequests = 60; // Максимум 60 запросов в минуту
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 1, 'start_time' => $now];
            return;
        }
        
        $rateData = $_SESSION[$key];
        
        // Если прошло больше минуты, сбрасываем счетчик
        if ($now - $rateData['start_time'] > $windowSize) {
            $_SESSION[$key] = ['count' => 1, 'start_time' => $now];
            return;
        }
        
        // Увеличиваем счетчик
        $_SESSION[$key]['count']++;
        
        // Проверяем лимит
        if ($_SESSION[$key]['count'] > $maxRequests) {
            $this->respondWithError('Too many requests', 429);
            return;
        }
    }

    /**
     * Получение IP адреса клиента
     */
    private function getClientIP(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Для X-Forwarded-For может быть список IP
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * Отправка ошибки
     */
    private function respondWithError(string $message, int $code): void
    {
        http_response_code($code);
        
        // Логирование попытки атаки
        $this->logSecurityEvent($message, $code);
        
        if ($this->isApiRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'error' => true,
                'message' => $message,
                'code' => $code
            ]);
        } else {
            echo "<h1>Security Error</h1><p>{$message}</p>";
        }
        
        exit;
    }

    /**
     * Проверка API запроса
     */
    private function isApiRequest(): bool
    {
        return strpos($_SERVER['REQUEST_URI'], '/api/') === 0 || 
               (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
    }

    /**
     * Логирование событий безопасности
     */
    private function logSecurityEvent(string $message, int $code): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'method' => $_SERVER['REQUEST_METHOD'] ?? '',
            'message' => $message,
            'code' => $code
        ];
        
        $logFile = ROOT_PATH . '/storage/logs/security.log';
        $logEntry = json_encode($logData) . "\n";
        
        // Создаем директорию если её нет
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Валидация SQL инъекций
     */
    public static function validateSqlInjection(string $input): bool
    {
        $sqlPatterns = [
            '/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i',
            '/(\b(OR|AND)\s+\d+\s*=\s*\d+)/i',
            '/(\b(OR|AND)\s+[\'"]?\w+[\'"]?\s*=\s*[\'"]?\w+[\'"]?)/i',
            '/(--|#|\/\*|\*\/)/i',
            '/(\bxp_\w+)/i'
        ];
        
        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return false;
            }
        }
        
        return true;
    }
}
