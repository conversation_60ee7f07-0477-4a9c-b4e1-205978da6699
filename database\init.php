<?php
/**
 * Скрипт инициализации базы данных AstroGenix
 */

// Подключаем автозагрузчик
require_once __DIR__ . '/../vendor/autoload.php';

// Определяем константы
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');

use App\Core\Database;

echo "=== AstroGenix Database Initialization ===\n\n";

try {
    // Загружаем конфигурацию
    $config = require CONFIG_PATH . '/app.php';
    
    // Создаем подключение к базе данных
    $database = new Database($config['database']);
    
    echo "Connected to database successfully.\n\n";
    
    // Выполняем миграции
    echo "Running migrations...\n";
    $database->migrate();
    
    echo "\n=== Database initialization completed successfully! ===\n";
    echo "You can now access your AstroGenix platform.\n\n";
    
    // Создаем администратора по умолчанию
    echo "Creating default admin user...\n";
    createDefaultAdmin($database);
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
    exit(1);
}

/**
 * Создание администратора по умолчанию
 */
function createDefaultAdmin(Database $database): void
{
    // Проверяем, есть ли уже администратор
    $existingAdmin = $database->fetch(
        "SELECT id FROM users WHERE role = 'admin' LIMIT 1"
    );
    
    if ($existingAdmin) {
        echo "Admin user already exists.\n";
        return;
    }
    
    // Создаем администратора
    $adminData = [
        'email' => '<EMAIL>',
        'password' => password_hash('admin123', PASSWORD_ARGON2ID),
        'first_name' => 'Admin',
        'last_name' => 'AstroGenix',
        'role' => 'admin',
        'status' => 'active',
        'referral_code' => 'ADMIN001',
        'email_verified_at' => date('Y-m-d H:i:s'),
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $adminId = $database->insert('users', $adminData);
    
    // Создаем балансы для администратора
    $balanceTypes = ['main', 'bonus', 'referral'];
    foreach ($balanceTypes as $type) {
        $database->insert('user_balances', [
            'user_id' => $adminId,
            'balance_type' => $type,
            'amount' => 0.00000000
        ]);
    }
    
    echo "Default admin created:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    echo "Please change the password after first login!\n\n";
}

/**
 * Создание тестовых данных (опционально)
 */
function createTestData(Database $database): void
{
    echo "Creating test data...\n";
    
    // Создаем тестового пользователя
    $testUserData = [
        'email' => '<EMAIL>',
        'password' => password_hash('password123', PASSWORD_ARGON2ID),
        'first_name' => 'Test',
        'last_name' => 'User',
        'role' => 'user',
        'status' => 'active',
        'referral_code' => 'TEST001',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $testUserId = $database->insert('users', $testUserData);
    
    // Создаем балансы для тестового пользователя
    $balanceTypes = ['main', 'bonus', 'referral'];
    foreach ($balanceTypes as $type) {
        $amount = $type === 'bonus' ? 10.00000000 : 0.00000000; // Приветственный бонус
        $database->insert('user_balances', [
            'user_id' => $testUserId,
            'balance_type' => $type,
            'amount' => $amount
        ]);
    }
    
    // Создаем тестовую новость
    $newsData = [
        'title' => 'Welcome to AstroGenix!',
        'slug' => 'welcome-to-astrogenix',
        'excerpt' => 'Start your journey with our premium USDT staking platform.',
        'content' => '<p>Welcome to AstroGenix, the premier USDT staking platform! Start earning daily profits up to 2.5% with our secure and reliable investment plans.</p>',
        'category' => 'announcement',
        'author_id' => 1, // Admin ID
        'status' => 'published',
        'published_at' => date('Y-m-d H:i:s'),
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $database->insert('news', $newsData);
    
    echo "Test data created successfully.\n";
}

// Раскомментируйте следующую строку для создания тестовых данных
// createTestData($database);
