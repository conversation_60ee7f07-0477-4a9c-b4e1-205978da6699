<?php

namespace App\Core;

use PDO;
use PDOException;

/**
 * Класс для работы с базой данных
 */
class Database
{
    private static ?PDO $connection = null;
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Получение соединения с базой данных
     */
    public function getConnection(): PDO
    {
        if (self::$connection === null) {
            $this->connect();
        }
        
        return self::$connection;
    }

    /**
     * Установка соединения
     */
    private function connect(): void
    {
        try {
            $config = $this->config['connections'][$this->config['default']];
            
            $dsn = sprintf(
                '%s:host=%s;port=%s;dbname=%s;charset=%s',
                $config['driver'],
                $config['host'],
                $config['port'],
                $config['database'],
                $config['charset']
            );

            self::$connection = new PDO(
                $dsn,
                $config['username'],
                $config['password'],
                $config['options']
            );

        } catch (PDOException $e) {
            throw new \Exception("Database connection failed: " . $e->getMessage());
        }
    }

    /**
     * Выполнение запроса
     */
    public function query(string $sql, array $params = []): \PDOStatement
    {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new \Exception("Query failed: " . $e->getMessage());
        }
    }

    /**
     * Получение одной записи
     */
    public function fetch(string $sql, array $params = []): ?array
    {
        $stmt = $this->query($sql, $params);
        $result = $stmt->fetch();
        return $result ?: null;
    }

    /**
     * Получение всех записей
     */
    public function fetchAll(string $sql, array $params = []): array
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Вставка записи
     */
    public function insert(string $table, array $data): int
    {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return (int) $this->getConnection()->lastInsertId();
    }

    /**
     * Обновление записи
     */
    public function update(string $table, array $data, array $where): int
    {
        $setClause = [];
        foreach (array_keys($data) as $column) {
            $setClause[] = "{$column} = :{$column}";
        }
        
        $whereClause = [];
        foreach (array_keys($where) as $column) {
            $whereClause[] = "{$column} = :where_{$column}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . 
               " WHERE " . implode(' AND ', $whereClause);
        
        // Объединяем параметры
        $params = $data;
        foreach ($where as $key => $value) {
            $params["where_{$key}"] = $value;
        }
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Удаление записи
     */
    public function delete(string $table, array $where): int
    {
        $whereClause = [];
        foreach (array_keys($where) as $column) {
            $whereClause[] = "{$column} = :{$column}";
        }
        
        $sql = "DELETE FROM {$table} WHERE " . implode(' AND ', $whereClause);
        $stmt = $this->query($sql, $where);
        
        return $stmt->rowCount();
    }

    /**
     * Начало транзакции
     */
    public function beginTransaction(): bool
    {
        return $this->getConnection()->beginTransaction();
    }

    /**
     * Подтверждение транзакции
     */
    public function commit(): bool
    {
        return $this->getConnection()->commit();
    }

    /**
     * Откат транзакции
     */
    public function rollback(): bool
    {
        return $this->getConnection()->rollback();
    }

    /**
     * Проверка существования таблицы
     */
    public function tableExists(string $table): bool
    {
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->fetch($sql, ['table' => $table]);
        return $result !== null;
    }

    /**
     * Выполнение миграций
     */
    public function migrate(): void
    {
        $migrationsPath = ROOT_PATH . '/database/migrations';
        
        if (!is_dir($migrationsPath)) {
            return;
        }

        // Создаем таблицу миграций если её нет
        $this->createMigrationsTable();

        $files = glob($migrationsPath . '/*.sql');
        sort($files);

        foreach ($files as $file) {
            $filename = basename($file);
            
            if (!$this->migrationExists($filename)) {
                $sql = file_get_contents($file);
                $this->getConnection()->exec($sql);
                $this->recordMigration($filename);
                
                echo "Migrated: {$filename}\n";
            }
        }
    }

    /**
     * Создание таблицы миграций
     */
    private function createMigrationsTable(): void
    {
        $sql = "CREATE TABLE IF NOT EXISTS migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration VARCHAR(255) NOT NULL,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $this->getConnection()->exec($sql);
    }

    /**
     * Проверка существования миграции
     */
    private function migrationExists(string $migration): bool
    {
        $result = $this->fetch(
            "SELECT id FROM migrations WHERE migration = :migration",
            ['migration' => $migration]
        );
        
        return $result !== null;
    }

    /**
     * Запись выполненной миграции
     */
    private function recordMigration(string $migration): void
    {
        $this->insert('migrations', ['migration' => $migration]);
    }
}
