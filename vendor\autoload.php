<?php

/**
 * Автозагрузчик классов для AstroGenix
 */

spl_autoload_register(function ($className) {
    // Преобразуем namespace в путь к файлу
    $classPath = str_replace('\\', DIRECTORY_SEPARATOR, $className);
    
    // Убираем префикс App\ и заменяем на app/
    if (strpos($classPath, 'App' . DIRECTORY_SEPARATOR) === 0) {
        $classPath = 'app' . DIRECTORY_SEPARATOR . substr($classPath, 4);
    }
    
    // Убираем префикс Database\ и заменяем на database/
    if (strpos($classPath, 'Database' . DIRECTORY_SEPARATOR) === 0) {
        $classPath = 'database' . DIRECTORY_SEPARATOR . substr($classPath, 9);
    }
    
    // Полный путь к файлу
    $filePath = ROOT_PATH . DIRECTORY_SEPARATOR . $classPath . '.php';
    
    // Загружаем файл если он существует
    if (file_exists($filePath)) {
        require_once $filePath;
        return true;
    }
    
    return false;
});

// Загружаем функции локализации
require_once ROOT_PATH . '/app/Core/Localization.php';
