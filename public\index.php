<?php
/**
 * AstroGenix - Premium USDT Staking Platform
 * Entry Point
 */

// Определяем константы
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PUBLIC_PATH', __DIR__);

// Автозагрузка классов
require_once ROOT_PATH . '/vendor/autoload.php';

// Загружаем конфигурацию
require_once CONFIG_PATH . '/app.php';

// Обработка ошибок
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Запуск приложения
try {
    $app = new App\Core\Application();
    $app->run();
} catch (Exception $e) {
    // Логирование ошибки
    error_log($e->getMessage());
    
    // Показываем пользователю дружелюбную ошибку
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal Server Error',
        'message' => 'Something went wrong. Please try again later.'
    ]);
}
