<?php
/**
 * AstroGenix - Установщик командной строки
 */

echo "=== AstroGenix Installation ===\n\n";

// Проверяем требования
echo "Checking requirements...\n";
$requirements = [
    'PHP >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
    'PDO MySQL' => extension_loaded('pdo_mysql'),
    'JSON' => extension_loaded('json'),
    'mbstring' => extension_loaded('mbstring'),
    'OpenSSL' => extension_loaded('openssl'),
];

$allOk = true;
foreach ($requirements as $req => $ok) {
    echo ($ok ? '✓' : '✗') . " {$req}\n";
    $allOk = $allOk && $ok;
}

if (!$allOk) {
    echo "\nPlease install missing requirements and try again.\n";
    exit(1);
}

echo "\n";

// Получаем настройки базы данных
echo "Database Configuration:\n";
echo "Host [localhost]: ";
$dbHost = trim(fgets(STDIN)) ?: 'localhost';

echo "Port [3306]: ";
$dbPort = trim(fgets(STDIN)) ?: '3306';

echo "Database name [astrogenix]: ";
$dbName = trim(fgets(STDIN)) ?: 'astrogenix';

echo "Username [root]: ";
$dbUser = trim(fgets(STDIN)) ?: 'root';

echo "Password []: ";
$dbPass = trim(fgets(STDIN));

try {
    echo "\nConnecting to database...\n";
    
    // Подключаемся к MySQL
    $dsn = "mysql:host={$dbHost};port={$dbPort};charset=utf8mb4";
    $pdo = new PDO($dsn, $dbUser, $dbPass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    echo "✓ Connected to MySQL\n";
    
    // Создаем базу данных
    echo "Creating database '{$dbName}'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `{$dbName}`");
    echo "✓ Database created\n";
    
    // Создаем .env файл
    echo "Creating .env file...\n";
    $envContent = "APP_ENV=production\n";
    $envContent .= "APP_DEBUG=false\n";
    $envContent .= "APP_KEY=" . base64_encode(random_bytes(32)) . "\n";
    $envContent .= "DB_HOST={$dbHost}\n";
    $envContent .= "DB_PORT={$dbPort}\n";
    $envContent .= "DB_DATABASE={$dbName}\n";
    $envContent .= "DB_USERNAME={$dbUser}\n";
    $envContent .= "DB_PASSWORD={$dbPass}\n";
    
    file_put_contents('.env', $envContent);
    echo "✓ .env file created\n";
    
    // Создаем директории
    echo "Creating directories...\n";
    if (!is_dir('storage')) mkdir('storage', 0755, true);
    if (!is_dir('storage/logs')) mkdir('storage/logs', 0755, true);
    if (!is_dir('public/uploads')) mkdir('public/uploads', 0755, true);
    echo "✓ Directories created\n";
    
    // Выполняем миграции
    echo "Running migrations...\n";
    $migrations = [
        'database/migrations/001_create_users_table.sql',
        'database/migrations/002_create_investments_table.sql',
        'database/migrations/003_create_referral_system.sql',
        'database/migrations/004_create_content_system.sql'
    ];
    
    foreach ($migrations as $migration) {
        if (file_exists($migration)) {
            echo "  - " . basename($migration) . "\n";
            $sql = file_get_contents($migration);
            $pdo->exec($sql);
        }
    }
    echo "✓ Migrations completed\n";
    
    // Создаем администратора
    echo "Creating admin user...\n";
    $adminPassword = password_hash('admin123', PASSWORD_ARGON2ID);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (email, password, first_name, last_name, role, status, referral_code, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    ");
    
    $stmt->execute([
        '<EMAIL>',
        $adminPassword,
        'Admin',
        'AstroGenix',
        'admin',
        'active',
        'ADMIN001'
    ]);
    
    $adminId = $pdo->lastInsertId();
    
    // Создаем балансы
    $balanceTypes = ['main', 'bonus', 'referral'];
    foreach ($balanceTypes as $type) {
        $stmt = $pdo->prepare("INSERT INTO user_balances (user_id, balance_type, amount) VALUES (?, ?, ?)");
        $stmt->execute([$adminId, $type, 0]);
    }
    
    echo "✓ Admin user created\n";
    
    // Создаем файл блокировки
    file_put_contents('storage/installed.lock', date('Y-m-d H:i:s'));
    
    echo "\n=== Installation Complete! ===\n";
    echo "Admin credentials:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    echo "\n⚠️  Please change the admin password after first login!\n";
    echo "\nYou can now access your AstroGenix platform.\n";
    
} catch (Exception $e) {
    echo "\n❌ Installation failed: " . $e->getMessage() . "\n";
    exit(1);
}
