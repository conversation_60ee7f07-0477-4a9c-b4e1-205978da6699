<?php
/**
 * AstroGenix - Простой установщик
 */

// Проверяем, что установка еще не выполнена
if (file_exists('.env') && file_exists('storage/installed.lock')) {
    die('AstroGenix уже установлен! Удалите файл storage/installed.lock для переустановки.');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Получаем данные
        $dbHost = $_POST['db_host'] ?? 'localhost';
        $dbPort = $_POST['db_port'] ?? '3306';
        $dbName = $_POST['db_name'] ?? 'astrogenix';
        $dbUser = $_POST['db_user'] ?? 'root';
        $dbPass = $_POST['db_pass'] ?? '';
        
        // Проверяем подключение
        $dsn = "mysql:host={$dbHost};port={$dbPort};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbUser, $dbPass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Создаем базу данных
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$dbName}`");
        
        // Создаем .env файл
        $envContent = "APP_ENV=production\n";
        $envContent .= "APP_DEBUG=false\n";
        $envContent .= "APP_KEY=" . base64_encode(random_bytes(32)) . "\n";
        $envContent .= "DB_HOST={$dbHost}\n";
        $envContent .= "DB_PORT={$dbPort}\n";
        $envContent .= "DB_DATABASE={$dbName}\n";
        $envContent .= "DB_USERNAME={$dbUser}\n";
        $envContent .= "DB_PASSWORD={$dbPass}\n";
        
        file_put_contents('.env', $envContent);
        
        // Создаем директории
        if (!is_dir('storage')) mkdir('storage', 0755, true);
        if (!is_dir('storage/logs')) mkdir('storage/logs', 0755, true);
        if (!is_dir('public/uploads')) mkdir('public/uploads', 0755, true);
        
        // Выполняем SQL миграции
        $migrations = [
            'database/migrations/001_create_users_table.sql',
            'database/migrations/002_create_investments_table.sql',
            'database/migrations/003_create_referral_system.sql',
            'database/migrations/004_create_content_system.sql'
        ];
        
        foreach ($migrations as $migration) {
            if (file_exists($migration)) {
                $sql = file_get_contents($migration);
                $pdo->exec($sql);
            }
        }
        
        // Создаем администратора
        $adminPassword = password_hash('admin123', PASSWORD_ARGON2ID);
        $pdo->exec("
            INSERT INTO users (email, password, first_name, last_name, role, status, referral_code, created_at, updated_at) 
            VALUES ('<EMAIL>', '{$adminPassword}', 'Admin', 'AstroGenix', 'admin', 'active', 'ADMIN001', NOW(), NOW())
        ");
        
        $adminId = $pdo->lastInsertId();
        
        // Создаем балансы для администратора
        $pdo->exec("INSERT INTO user_balances (user_id, balance_type, amount) VALUES ({$adminId}, 'main', 0)");
        $pdo->exec("INSERT INTO user_balances (user_id, balance_type, amount) VALUES ({$adminId}, 'bonus', 0)");
        $pdo->exec("INSERT INTO user_balances (user_id, balance_type, amount) VALUES ({$adminId}, 'referral', 0)");
        
        // Создаем файл блокировки
        file_put_contents('storage/installed.lock', date('Y-m-d H:i:s'));
        
        $success = 'Установка завершена успешно!';
        
    } catch (Exception $e) {
        $error = 'Ошибка установки: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Установка AstroGenix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0A0A0F 0%, #1A1A2E 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            max-width: 500px;
            background: rgba(26, 26, 46, 0.9);
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1 {
            text-align: center;
            color: #00D4FF;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #B8BCC8;
        }
        input {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            color: white;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #00D4FF;
        }
        button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #00D4FF 0%, #8B5CF6 100%);
            border: none;
            border-radius: 5px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }
        button:hover {
            opacity: 0.9;
        }
        .error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid red;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid green;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .info {
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid #00D4FF;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .requirements {
            margin-bottom: 20px;
        }
        .req-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        .req-ok { color: #00FF88; margin-right: 10px; }
        .req-error { color: #FF4444; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Установка AstroGenix</h1>
        
        <?php if ($success): ?>
            <div class="success">
                <strong>✅ <?= $success ?></strong><br><br>
                <strong>Данные для входа:</strong><br>
                Email: <EMAIL><br>
                Пароль: admin123<br><br>
                <a href="/login" style="color: #00D4FF;">Перейти к входу</a>
            </div>
        <?php else: ?>
            
            <?php if ($error): ?>
                <div class="error">❌ <?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
            
            <div class="info">
                <strong>Проверка требований:</strong>
            </div>
            
            <div class="requirements">
                <?php
                $requirements = [
                    'PHP >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
                    'PDO MySQL' => extension_loaded('pdo_mysql'),
                    'JSON' => extension_loaded('json'),
                    'mbstring' => extension_loaded('mbstring'),
                    'OpenSSL' => extension_loaded('openssl'),
                ];
                
                foreach ($requirements as $req => $ok) {
                    echo '<div class="req-item">';
                    echo '<span class="' . ($ok ? 'req-ok">✓' : 'req-error">✗') . '</span>';
                    echo $req;
                    echo '</div>';
                }
                ?>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label>Хост базы данных:</label>
                    <input type="text" name="db_host" value="<?= $_POST['db_host'] ?? 'localhost' ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Порт:</label>
                    <input type="text" name="db_port" value="<?= $_POST['db_port'] ?? '3306' ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Имя базы данных:</label>
                    <input type="text" name="db_name" value="<?= $_POST['db_name'] ?? 'astrogenix' ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Пользователь:</label>
                    <input type="text" name="db_user" value="<?= $_POST['db_user'] ?? 'root' ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Пароль:</label>
                    <input type="password" name="db_pass" value="<?= $_POST['db_pass'] ?? '' ?>" placeholder="Оставьте пустым если пароля нет">
                </div>
                
                <button type="submit">🚀 Установить AstroGenix</button>
            </form>
            
        <?php endif; ?>
    </div>
</body>
</html>
