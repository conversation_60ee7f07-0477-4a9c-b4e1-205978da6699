<?php

namespace App\Models;

/**
 * Модель новостей
 */
class News extends BaseModel
{
    protected string $table = 'news';
    
    protected array $fillable = [
        'title', 'slug', 'excerpt', 'content', 'featured_image', 'category',
        'tags', 'meta_title', 'meta_description', 'author_id', 'status',
        'published_at', 'views_count'
    ];
    
    protected array $casts = [
        'id' => 'int',
        'author_id' => 'int',
        'views_count' => 'int',
        'tags' => 'json'
    ];

    /**
     * Получение опубликованных новостей
     */
    public function getPublishedNews(int $limit = 10, int $offset = 0): array
    {
        $sql = "
            SELECT n.*, u.first_name, u.last_name
            FROM {$this->table} n
            LEFT JOIN users u ON n.author_id = u.id
            WHERE n.status = 'published' 
            AND n.published_at <= NOW()
            ORDER BY n.published_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        return $this->query($sql, [
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    /**
     * Получение последних новостей
     */
    public function getLatestNews(int $limit = 5): array
    {
        return $this->getPublishedNews($limit);
    }

    /**
     * Получение новости по slug
     */
    public function getBySlug(string $slug): ?array
    {
        $sql = "
            SELECT n.*, u.first_name, u.last_name
            FROM {$this->table} n
            LEFT JOIN users u ON n.author_id = u.id
            WHERE n.slug = :slug 
            AND n.status = 'published'
            AND n.published_at <= NOW()
            LIMIT 1
        ";
        
        $result = $this->db->fetch($sql, ['slug' => $slug]);
        
        if ($result) {
            // Увеличиваем счетчик просмотров
            $this->incrementViews($result['id']);
            return $this->processResult($result);
        }
        
        return null;
    }

    /**
     * Получение новостей по категории
     */
    public function getByCategory(string $category, int $limit = 10, int $offset = 0): array
    {
        $sql = "
            SELECT n.*, u.first_name, u.last_name
            FROM {$this->table} n
            LEFT JOIN users u ON n.author_id = u.id
            WHERE n.category = :category 
            AND n.status = 'published'
            AND n.published_at <= NOW()
            ORDER BY n.published_at DESC
            LIMIT :limit OFFSET :offset
        ";
        
        return $this->query($sql, [
            'category' => $category,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    /**
     * Поиск новостей
     */
    public function search(string $query, int $limit = 10): array
    {
        $sql = "
            SELECT n.*, u.first_name, u.last_name
            FROM {$this->table} n
            LEFT JOIN users u ON n.author_id = u.id
            WHERE (n.title LIKE :query OR n.content LIKE :query OR n.excerpt LIKE :query)
            AND n.status = 'published'
            AND n.published_at <= NOW()
            ORDER BY n.published_at DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, [
            'query' => "%{$query}%",
            'limit' => $limit
        ]);
    }

    /**
     * Получение популярных новостей
     */
    public function getPopularNews(int $limit = 5, int $days = 30): array
    {
        $sql = "
            SELECT n.*, u.first_name, u.last_name
            FROM {$this->table} n
            LEFT JOIN users u ON n.author_id = u.id
            WHERE n.status = 'published'
            AND n.published_at <= NOW()
            AND n.published_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            ORDER BY n.views_count DESC, n.published_at DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, [
            'days' => $days,
            'limit' => $limit
        ]);
    }

    /**
     * Получение связанных новостей
     */
    public function getRelatedNews(int $newsId, string $category, int $limit = 3): array
    {
        $sql = "
            SELECT n.*, u.first_name, u.last_name
            FROM {$this->table} n
            LEFT JOIN users u ON n.author_id = u.id
            WHERE n.id != :news_id
            AND n.category = :category
            AND n.status = 'published'
            AND n.published_at <= NOW()
            ORDER BY n.published_at DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, [
            'news_id' => $newsId,
            'category' => $category,
            'limit' => $limit
        ]);
    }

    /**
     * Получение всех категорий
     */
    public function getCategories(): array
    {
        $sql = "
            SELECT category, COUNT(*) as count
            FROM {$this->table}
            WHERE status = 'published'
            AND published_at <= NOW()
            AND category IS NOT NULL
            GROUP BY category
            ORDER BY count DESC, category ASC
        ";
        
        return $this->query($sql);
    }

    /**
     * Увеличение счетчика просмотров
     */
    public function incrementViews(int $newsId): bool
    {
        $sql = "UPDATE {$this->table} SET views_count = views_count + 1 WHERE id = :id";
        $this->db->query($sql, ['id' => $newsId]);
        return true;
    }

    /**
     * Создание slug из заголовка
     */
    public function generateSlug(string $title, int $excludeId = null): string
    {
        // Транслитерация и очистка
        $slug = $this->transliterate($title);
        $slug = strtolower($slug);
        $slug = preg_replace('/[^a-z0-9\-]/', '', $slug);
        $slug = preg_replace('/\-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // Проверка уникальности
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug, $excludeId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Проверка существования slug
     */
    private function slugExists(string $slug, int $excludeId = null): bool
    {
        $sql = "SELECT id FROM {$this->table} WHERE slug = :slug";
        $params = ['slug' => $slug];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params['exclude_id'] = $excludeId;
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result !== null;
    }

    /**
     * Транслитерация текста
     */
    private function transliterate(string $text): string
    {
        $transliteration = [
            'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd',
            'е' => 'e', 'ё' => 'yo', 'ж' => 'zh', 'з' => 'z', 'и' => 'i',
            'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n',
            'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't',
            'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch',
            'ш' => 'sh', 'щ' => 'sch', 'ъ' => '', 'ы' => 'y', 'ь' => '',
            'э' => 'e', 'ю' => 'yu', 'я' => 'ya',
            'А' => 'A', 'Б' => 'B', 'В' => 'V', 'Г' => 'G', 'Д' => 'D',
            'Е' => 'E', 'Ё' => 'Yo', 'Ж' => 'Zh', 'З' => 'Z', 'И' => 'I',
            'Й' => 'Y', 'К' => 'K', 'Л' => 'L', 'М' => 'M', 'Н' => 'N',
            'О' => 'O', 'П' => 'P', 'Р' => 'R', 'С' => 'S', 'Т' => 'T',
            'У' => 'U', 'Ф' => 'F', 'Х' => 'H', 'Ц' => 'Ts', 'Ч' => 'Ch',
            'Ш' => 'Sh', 'Щ' => 'Sch', 'Ъ' => '', 'Ы' => 'Y', 'Ь' => '',
            'Э' => 'E', 'Ю' => 'Yu', 'Я' => 'Ya'
        ];
        
        return strtr($text, $transliteration);
    }

    /**
     * Получение архива новостей по месяцам
     */
    public function getArchive(): array
    {
        $sql = "
            SELECT 
                YEAR(published_at) as year,
                MONTH(published_at) as month,
                COUNT(*) as count
            FROM {$this->table}
            WHERE status = 'published'
            AND published_at <= NOW()
            GROUP BY YEAR(published_at), MONTH(published_at)
            ORDER BY year DESC, month DESC
        ";
        
        return $this->query($sql);
    }

    /**
     * Получение статистики новостей
     */
    public function getStats(): array
    {
        $sql = "
            SELECT 
                COUNT(*) as total_news,
                COUNT(CASE WHEN status = 'published' THEN 1 END) as published_news,
                COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_news,
                SUM(views_count) as total_views,
                AVG(views_count) as avg_views
            FROM {$this->table}
        ";
        
        $result = $this->db->fetch($sql);
        
        return [
            'total_news' => (int) $result['total_news'],
            'published_news' => (int) $result['published_news'],
            'draft_news' => (int) $result['draft_news'],
            'total_views' => (int) $result['total_views'],
            'avg_views' => (float) $result['avg_views']
        ];
    }
}
