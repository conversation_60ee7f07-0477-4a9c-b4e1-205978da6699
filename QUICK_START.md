# 🚀 AstroGenix - Быстрый старт

## 📋 Что создано

Полнофункциональная платформа для USDT стейкинга **AstroGenix** со следующими возможностями:

### ✨ Основные функции
- **4 инвестиционных плана** с доходностью 1-2.5% в день
- **3-уровневая реферальная программа** (7%, 3%, 1%)
- **Система заданий (квестов)** с наградами
- **Ручная система депозитов/выводов** с админ-подтверждением
- **Блог/новости** с SEO-оптимизацией
- **Двуязычность** (русский/английский) с автоопределением
- **Премиум дизайн** в стиле Binance/Coinbase
- **Полная система безопасности**

### 🛡️ Безопасность
- Защита от CSRF, XSS, SQL-инъекций
- Rate limiting (ограничение частоты запросов)
- Безопасные HTTP заголовки
- Хеширование паролей Argon2ID
- Логирование всех действий

### 🎨 Дизайн
- Темная тема с неоновыми акцентами
- Glassmorphism эффекты
- Анимации майнинга
- Mobile-first подход
- SVG иконки и плавные переходы

## 🚀 Быстрая установка

### Вариант 1: Веб-установщик (Рекомендуется)

1. **Загрузите файлы** на ваш веб-сервер
2. **Откройте в браузере**: `http://yourdomain.com/install.php`
3. **Следуйте инструкциям** веб-установщика
4. **Готово!** Платформа установлена и готова к работе

### Вариант 2: Ручная установка

1. **Создайте базу данных MySQL:**
```sql
CREATE DATABASE astrogenix CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **Настройте .env файл:**
```bash
cp .env.example .env
# Отредактируйте .env с вашими настройками БД
```

3. **Запустите инициализацию:**
```bash
php database/init.php
```

## 🔑 Доступы по умолчанию

После установки используйте:

**Администратор:**
- Email: `<EMAIL>`
- Пароль: `admin123`

⚠️ **Обязательно смените пароль после первого входа!**

## 📁 Структура файлов

```
astrogenix/
├── 📁 app/                    # Основное приложение
│   ├── 📁 Controllers/        # Контроллеры (AuthController, HomeController, etc.)
│   ├── 📁 Models/            # Модели (User, UserBalance, Transaction, etc.)
│   ├── 📁 Views/             # Представления (HTML шаблоны)
│   ├── 📁 Core/              # Ядро (Application, Database, Router, etc.)
│   └── 📁 Middleware/        # Промежуточное ПО (SecurityMiddleware)
├── 📁 config/                # Конфигурация
├── 📁 database/              # База данных
│   ├── 📁 migrations/        # SQL миграции (4 файла)
│   └── 📄 init.php          # Скрипт инициализации
├── 📁 lang/                  # Локализация
│   ├── 📁 en/               # Английские переводы
│   └── 📁 ru/               # Русские переводы
├── 📁 public/                # Публичные файлы
│   ├── 📁 assets/           # CSS, JS, изображения
│   ├── 📄 .htaccess         # Apache конфигурация
│   └── 📄 index.php         # Точка входа
├── 📁 storage/logs/          # Логи приложения
├── 📁 vendor/                # Автозагрузчик
├── 📄 .env.example          # Пример конфигурации
├── 📄 composer.json         # Composer конфигурация
├── 📄 install.php           # Веб-установщик
└── 📄 README.md             # Подробная документация
```

## ⚙️ Основные настройки

### Планы инвестиций
Настраиваются в `config/app.php`:
- **Starter**: 1% в день (10-100 USDT)
- **Basic**: 1.5% в день (101-500 USDT)
- **Advanced**: 2% в день (501-2000 USDT)
- **Professional**: 2.5% в день (2001-10000 USDT)

### Реферальная программа
- **1-й уровень**: 7% с инвестиций
- **2-й уровень**: 3% с инвестиций
- **3-й уровень**: 1% с инвестиций

### Бонусы и лимиты
- **Приветственный бонус**: 10 USDT
- **Минимальный вывод**: 5 USDT
- **Комиссия за вывод**: 2%

## 🔧 Настройка веб-сервера

### Apache
Убедитесь, что DocumentRoot указывает на папку `public/`:
```apache
<VirtualHost *:80>
    DocumentRoot /path/to/astrogenix/public
    ServerName yourdomain.com
</VirtualHost>
```

### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/astrogenix/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 🔄 Автоматические процессы

### Начисление ежедневной прибыли
Создайте cron-задачу:
```bash
# Добавьте в crontab (crontab -e)
0 0 * * * php /path/to/astrogenix/scripts/daily_profit.php
```

### Очистка логов
```bash
# Очистка старых логов (раз в неделю)
0 0 * * 0 find /path/to/astrogenix/storage/logs -name "*.log" -mtime +30 -delete
```

## 📊 Функционал платформы

### Для пользователей:
- ✅ Регистрация с реферальным кодом
- ✅ Приветственный бонус 10 USDT
- ✅ 4 инвестиционных плана
- ✅ Ежедневное начисление прибыли
- ✅ Реферальная программа 3 уровня
- ✅ Система заданий с наградами
- ✅ Депозиты/выводы USDT
- ✅ История транзакций
- ✅ Тикеты поддержки

### Для администраторов:
- ✅ Управление пользователями
- ✅ Одобрение депозитов/выводов
- ✅ Управление новостями
- ✅ Настройка заданий
- ✅ Статистика и аналитика
- ✅ Логи безопасности
- ✅ Настройки платформы

## 🌐 Локализация

Платформа поддерживает:
- **Английский** (по умолчанию)
- **Русский** (автоопределение для СНГ)

Переводы находятся в папке `lang/`:
- `lang/en/` - английские переводы
- `lang/ru/` - русские переводы

## 🛡️ Безопасность

### Рекомендации:
1. **Используйте HTTPS** в продакшене
2. **Смените пароль администратора**
3. **Настройте файрвол**
4. **Регулярные бэкапы БД**
5. **Мониторинг логов**

### Логи безопасности:
- `storage/logs/security.log` - события безопасности
- `storage/logs/user_actions.log` - действия пользователей

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в `storage/logs/`
2. Убедитесь в правильности конфигурации
3. Проверьте права доступа к файлам (755)

## 🎯 Что дальше?

1. **Настройте SSL сертификат**
2. **Добавьте реальные платежные системы**
3. **Настройте email уведомления**
4. **Добавьте Google Analytics**
5. **Настройте мониторинг**

---

**🚀 AstroGenix готов к запуску!**

Платформа полностью функциональна и готова для использования. Все основные функции реализованы, безопасность настроена, дизайн адаптивен.

**Удачного запуска! 🌟**
