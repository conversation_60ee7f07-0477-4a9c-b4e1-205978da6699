<?php

namespace App\Core;

/**
 * Класс для работы с локализацией
 */
class Localization
{
    private array $config;
    private string $currentLocale;
    private array $translations = [];
    private array $cisCountries = [
        'RU', 'BY', 'KZ', 'KG', 'TJ', 'UZ', 'AM', 'AZ', 'GE', 'MD', 'UA'
    ];

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->detectLocale();
        $this->loadTranslations();
    }

    /**
     * Автоматическое определение локали
     */
    private function detectLocale(): void
    {
        // Проверяем сессию
        if (isset($_SESSION['locale']) && in_array($_SESSION['locale'], $this->config['supported_locales'])) {
            $this->currentLocale = $_SESSION['locale'];
            return;
        }

        // Определяем по IP (упрощенная версия)
        $userCountry = $this->getUserCountry();
        if (in_array($userCountry, $this->cisCountries)) {
            $this->currentLocale = 'ru';
        } else {
            $this->currentLocale = $this->config['locale'];
        }

        // Сохраняем в сессию
        $_SESSION['locale'] = $this->currentLocale;
    }

    /**
     * Получение страны пользователя (упрощенная версия)
     */
    private function getUserCountry(): string
    {
        // В реальном проекте здесь должна быть интеграция с GeoIP
        // Для демонстрации используем заголовки
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        
        if (strpos($acceptLanguage, 'ru') !== false) {
            return 'RU';
        }
        
        return 'US';
    }

    /**
     * Загрузка переводов
     */
    private function loadTranslations(): void
    {
        $langPath = ROOT_PATH . '/lang/' . $this->currentLocale;
        
        if (!is_dir($langPath)) {
            return;
        }

        $files = glob($langPath . '/*.php');
        
        foreach ($files as $file) {
            $key = basename($file, '.php');
            $this->translations[$key] = require $file;
        }
    }

    /**
     * Получение перевода
     */
    public function get(string $key, array $params = []): string
    {
        $keys = explode('.', $key);
        $value = $this->translations;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $key; // Возвращаем ключ если перевод не найден
            }
            $value = $value[$k];
        }

        // Замена параметров
        if (!empty($params)) {
            foreach ($params as $param => $replacement) {
                $value = str_replace(':' . $param, $replacement, $value);
            }
        }

        return $value;
    }

    /**
     * Установка локали
     */
    public function setLocale(string $locale): void
    {
        if (in_array($locale, $this->config['supported_locales'])) {
            $this->currentLocale = $locale;
            $_SESSION['locale'] = $locale;
            $this->loadTranslations();
        }
    }

    /**
     * Получение текущей локали
     */
    public function getCurrentLocale(): string
    {
        return $this->currentLocale;
    }

    /**
     * Получение поддерживаемых локалей
     */
    public function getSupportedLocales(): array
    {
        return $this->config['supported_locales'];
    }

    /**
     * Проверка существования перевода
     */
    public function has(string $key): bool
    {
        $keys = explode('.', $key);
        $value = $this->translations;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return false;
            }
            $value = $value[$k];
        }

        return true;
    }

    /**
     * Форматирование числа согласно локали
     */
    public function formatNumber(float $number, int $decimals = 2): string
    {
        $decimalSeparator = $this->currentLocale === 'ru' ? ',' : '.';
        $thousandsSeparator = $this->currentLocale === 'ru' ? ' ' : ',';
        
        return number_format($number, $decimals, $decimalSeparator, $thousandsSeparator);
    }

    /**
     * Форматирование валюты
     */
    public function formatCurrency(float $amount, string $currency = 'USDT'): string
    {
        $formatted = $this->formatNumber($amount, 2);
        
        if ($this->currentLocale === 'ru') {
            return $formatted . ' ' . $currency;
        }
        
        return $currency . ' ' . $formatted;
    }

    /**
     * Форматирование даты
     */
    public function formatDate(\DateTime $date, string $format = null): string
    {
        if ($format === null) {
            $format = $this->currentLocale === 'ru' ? 'd.m.Y H:i' : 'Y-m-d H:i';
        }
        
        return $date->format($format);
    }

    /**
     * Получение названия месяца
     */
    public function getMonthName(int $month): string
    {
        $months = $this->get('common.months', []);
        return $months[$month - 1] ?? $month;
    }

    /**
     * Получение названия дня недели
     */
    public function getDayName(int $day): string
    {
        $days = $this->get('common.days', []);
        return $days[$day] ?? $day;
    }
}

/**
 * Глобальная функция для перевода
 */
function t(string $key, array $params = []): string
{
    static $localization = null;
    
    if ($localization === null) {
        $config = require CONFIG_PATH . '/app.php';
        $localization = new Localization($config['app']);
    }
    
    return $localization->get($key, $params);
}
