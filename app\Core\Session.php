<?php

namespace App\Core;

/**
 * Класс для работы с сессиями
 */
class Session
{
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->start();
    }

    /**
     * Запуск сессии
     */
    private function start(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            // Настройки безопасности сессии
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            
            session_name($this->config['session_name']);
            session_start();
            
            // Регенерация ID сессии для безопасности
            if (!isset($_SESSION['initiated'])) {
                session_regenerate_id(true);
                $_SESSION['initiated'] = true;
            }
        }
    }

    /**
     * Установка значения в сессию
     */
    public function set(string $key, $value): void
    {
        $_SESSION[$key] = $value;
    }

    /**
     * Получение значения из сессии
     */
    public function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Проверка существования ключа
     */
    public function has(string $key): bool
    {
        return isset($_SESSION[$key]);
    }

    /**
     * Удаление значения из сессии
     */
    public function remove(string $key): void
    {
        unset($_SESSION[$key]);
    }

    /**
     * Очистка всей сессии
     */
    public function clear(): void
    {
        $_SESSION = [];
    }

    /**
     * Уничтожение сессии
     */
    public function destroy(): void
    {
        $this->clear();
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }

    /**
     * Генерация CSRF токена
     */
    public function generateCsrfToken(): string
    {
        $token = bin2hex(random_bytes(32));
        $this->set('csrf_token', $token);
        $this->set('csrf_token_time', time());
        return $token;
    }

    /**
     * Проверка CSRF токена
     */
    public function validateCsrfToken(string $token): bool
    {
        $sessionToken = $this->get('csrf_token');
        $tokenTime = $this->get('csrf_token_time', 0);
        
        // Проверяем время жизни токена (1 час)
        if (time() - $tokenTime > 3600) {
            return false;
        }
        
        return hash_equals($sessionToken, $token);
    }

    /**
     * Установка flash сообщения
     */
    public function flash(string $key, string $message): void
    {
        $_SESSION['flash'][$key] = $message;
    }

    /**
     * Получение flash сообщения
     */
    public function getFlash(string $key): ?string
    {
        $message = $_SESSION['flash'][$key] ?? null;
        unset($_SESSION['flash'][$key]);
        return $message;
    }

    /**
     * Проверка наличия flash сообщения
     */
    public function hasFlash(string $key): bool
    {
        return isset($_SESSION['flash'][$key]);
    }

    /**
     * Получение всех flash сообщений
     */
    public function getAllFlash(): array
    {
        $messages = $_SESSION['flash'] ?? [];
        $_SESSION['flash'] = [];
        return $messages;
    }

    /**
     * Установка пользователя в сессию
     */
    public function setUser(array $user): void
    {
        $this->set('user_id', $user['id']);
        $this->set('user_email', $user['email']);
        $this->set('user_role', $user['role'] ?? 'user');
        $this->set('logged_in', true);
    }

    /**
     * Получение ID пользователя
     */
    public function getUserId(): ?int
    {
        return $this->get('user_id');
    }

    /**
     * Проверка авторизации
     */
    public function isLoggedIn(): bool
    {
        return $this->get('logged_in', false) && $this->getUserId();
    }

    /**
     * Проверка роли администратора
     */
    public function isAdmin(): bool
    {
        return $this->isLoggedIn() && $this->get('user_role') === 'admin';
    }

    /**
     * Выход пользователя
     */
    public function logout(): void
    {
        $this->remove('user_id');
        $this->remove('user_email');
        $this->remove('user_role');
        $this->remove('logged_in');
        
        // Регенерируем ID сессии для безопасности
        session_regenerate_id(true);
    }

    /**
     * Получение ID сессии
     */
    public function getId(): string
    {
        return session_id();
    }
}
