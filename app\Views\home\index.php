<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-background">
        <div class="hero-particles"></div>
        <div class="hero-glow"></div>
    </div>
    
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    <?= $t('home.hero.title') ?>
                    <span class="gradient-text">AstroGenix</span>
                </h1>
                <p class="hero-subtitle">
                    <?= $t('home.hero.subtitle') ?>
                </p>
                <div class="hero-features">
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                        </svg>
                        <span><?= $t('home.features.daily_profits') ?></span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span><?= $t('home.features.secure_platform') ?></span>
                    </div>
                    <div class="feature-item">
                        <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                        </svg>
                        <span><?= $t('home.features.referral_program') ?></span>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="/register" class="btn btn-primary btn-lg">
                        <svg class="btn-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        <?= $t('home.cta.start_earning') ?>
                    </a>
                    <a href="/login" class="btn btn-outline btn-lg">
                        <?= $t('common.login') ?>
                    </a>
                </div>
                <div class="welcome-bonus">
                    <div class="bonus-badge">
                        <span class="bonus-amount"><?= $welcomeBonus ?> USDT</span>
                        <span class="bonus-text"><?= $t('home.welcome_bonus') ?></span>
                    </div>
                </div>
            </div>
            
            <div class="hero-visual">
                <div class="mining-container">
                    <div class="mining-animation">
                        <div class="mining-core">
                            <div class="core-inner"></div>
                            <div class="core-rings">
                                <div class="ring ring-1"></div>
                                <div class="ring ring-2"></div>
                                <div class="ring ring-3"></div>
                            </div>
                        </div>
                        <div class="mining-particles">
                            <div class="particle"></div>
                            <div class="particle"></div>
                            <div class="particle"></div>
                            <div class="particle"></div>
                            <div class="particle"></div>
                        </div>
                    </div>
                    <div class="mining-stats">
                        <div class="stat-item">
                            <span class="stat-value" data-format="currency"><?= number_format($stats['total_invested'], 2) ?></span>
                            <span class="stat-label"><?= $t('home.stats.total_invested') ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value"><?= number_format($stats['total_users']) ?></span>
                            <span class="stat-label"><?= $t('home.stats.total_users') ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" data-format="currency"><?= number_format($stats['total_paid'], 2) ?></span>
                            <span class="stat-label"><?= $t('home.stats.total_paid') ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Investment Plans Section -->
<section class="plans-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?= $t('home.plans.title') ?></h2>
            <p class="section-subtitle"><?= $t('home.plans.subtitle') ?></p>
        </div>
        
        <div class="plans-grid">
            <?php foreach ($investmentPlans as $planId => $plan): ?>
                <div class="plan-card glass-card" data-animate="fadeInUp">
                    <div class="plan-header">
                        <h3 class="plan-name"><?= $plan['name'] ?></h3>
                        <div class="plan-percentage">
                            <span class="percentage-value"><?= $plan['daily_percent'] ?>%</span>
                            <span class="percentage-label"><?= $t('home.plans.daily') ?></span>
                        </div>
                    </div>
                    
                    <div class="plan-features">
                        <div class="feature">
                            <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                            </svg>
                            <span><?= $t('home.plans.min_amount') ?>: <?= number_format($plan['min_amount'], 0) ?> USDT</span>
                        </div>
                        <div class="feature">
                            <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                            </svg>
                            <span><?= $t('home.plans.max_amount') ?>: <?= number_format($plan['max_amount'], 0) ?> USDT</span>
                        </div>
                        <div class="feature">
                            <svg class="feature-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            <span><?= $t('home.plans.duration') ?>: 365 <?= $t('home.plans.days') ?></span>
                        </div>
                    </div>
                    
                    <div class="plan-footer">
                        <a href="/register" class="btn btn-primary w-full">
                            <?= $t('home.plans.choose_plan') ?>
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Platform Stats Section -->
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card glass-card" data-animate="fadeInUp">
                <div class="stat-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($stats['total_users']) ?></div>
                    <div class="stat-label"><?= $t('home.stats.total_users') ?></div>
                </div>
            </div>
            
            <div class="stat-card glass-card" data-animate="fadeInUp">
                <div class="stat-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($stats['total_invested'], 0) ?></div>
                    <div class="stat-label"><?= $t('home.stats.total_invested') ?></div>
                </div>
            </div>
            
            <div class="stat-card glass-card" data-animate="fadeInUp">
                <div class="stat-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($stats['total_paid'], 0) ?></div>
                    <div class="stat-label"><?= $t('home.stats.total_paid') ?></div>
                </div>
            </div>
            
            <div class="stat-card glass-card" data-animate="fadeInUp">
                <div class="stat-icon">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?= number_format($stats['active_investments']) ?></div>
                    <div class="stat-label"><?= $t('home.stats.active_investments') ?></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Latest News Section -->
<?php if (!empty($news)): ?>
<section class="news-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title"><?= $t('home.news.title') ?></h2>
            <a href="/news" class="section-link"><?= $t('home.news.view_all') ?></a>
        </div>
        
        <div class="news-grid">
            <?php foreach ($news as $article): ?>
                <article class="news-card glass-card" data-animate="fadeInUp">
                    <?php if ($article['featured_image']): ?>
                        <div class="news-image">
                            <img src="<?= htmlspecialchars($article['featured_image']) ?>" alt="<?= htmlspecialchars($article['title']) ?>">
                        </div>
                    <?php endif; ?>
                    
                    <div class="news-content">
                        <div class="news-meta">
                            <span class="news-date"><?= date('M d, Y', strtotime($article['published_at'])) ?></span>
                            <?php if ($article['category']): ?>
                                <span class="news-category"><?= htmlspecialchars($article['category']) ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <h3 class="news-title">
                            <a href="/news/<?= htmlspecialchars($article['slug']) ?>">
                                <?= htmlspecialchars($article['title']) ?>
                            </a>
                        </h3>
                        
                        <?php if ($article['excerpt']): ?>
                            <p class="news-excerpt"><?= htmlspecialchars($article['excerpt']) ?></p>
                        <?php endif; ?>
                        
                        <div class="news-footer">
                            <div class="news-author">
                                <?= htmlspecialchars($article['first_name'] . ' ' . $article['last_name']) ?>
                            </div>
                            <div class="news-views">
                                <svg class="view-icon" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                </svg>
                                <?= number_format($article['views_count']) ?>
                            </div>
                        </div>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title"><?= $t('home.cta.title') ?></h2>
            <p class="cta-subtitle"><?= $t('home.cta.subtitle') ?></p>
            <div class="cta-actions">
                <a href="/register" class="btn btn-primary btn-lg">
                    <?= $t('home.cta.get_started') ?>
                </a>
                <a href="/login" class="btn btn-outline btn-lg">
                    <?= $t('common.login') ?>
                </a>
            </div>
        </div>
    </div>
</section>
