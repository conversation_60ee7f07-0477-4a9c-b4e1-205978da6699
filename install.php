<?php
/**
 * AstroGenix Installation Script
 * Простой веб-интерфейс для установки платформы
 */

// Проверяем, запущен ли скрипт через веб-браузер
if (php_sapi_name() !== 'cli') {
    // Веб-интерфейс установки
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AstroGenix Installation</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #0A0A0F 0%, #1A1A2E 100%);
                color: #FFFFFF;
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                max-width: 600px;
                background: rgba(26, 26, 46, 0.8);
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 16px;
                padding: 40px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            .logo {
                text-align: center;
                margin-bottom: 30px;
            }
            .logo h1 {
                background: linear-gradient(135deg, #00D4FF 0%, #8B5CF6 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                font-size: 2.5rem;
                margin: 0;
            }
            .form-group {
                margin-bottom: 20px;
            }
            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
                color: #B8BCC8;
            }
            input[type="text"], input[type="password"] {
                width: 100%;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                color: #FFFFFF;
                font-size: 14px;
                box-sizing: border-box;
            }
            input[type="text"]:focus, input[type="password"]:focus {
                outline: none;
                border-color: #00D4FF;
                box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
            }
            .btn {
                background: linear-gradient(135deg, #00D4FF 0%, #8B5CF6 100%);
                color: white;
                border: none;
                padding: 14px 28px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                width: 100%;
                transition: transform 0.2s ease;
            }
            .btn:hover {
                transform: translateY(-2px);
            }
            .btn:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }
            .alert {
                padding: 16px;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            .alert-success {
                background: rgba(0, 255, 136, 0.1);
                border: 1px solid rgba(0, 255, 136, 0.3);
                color: #00FF88;
            }
            .alert-error {
                background: rgba(239, 68, 68, 0.1);
                border: 1px solid rgba(239, 68, 68, 0.3);
                color: #EF4444;
            }
            .requirements {
                background: rgba(255, 255, 255, 0.05);
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
            }
            .req-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }
            .req-status {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                margin-right: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
            }
            .req-ok {
                background: #00FF88;
                color: #000;
            }
            .req-error {
                background: #EF4444;
                color: #FFF;
            }
            .step {
                display: none;
            }
            .step.active {
                display: block;
            }
            .progress {
                width: 100%;
                height: 4px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 2px;
                margin-bottom: 30px;
                overflow: hidden;
            }
            .progress-bar {
                height: 100%;
                background: linear-gradient(135deg, #00D4FF 0%, #8B5CF6 100%);
                transition: width 0.3s ease;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">
                <h1>AstroGenix</h1>
                <p>Premium USDT Staking Platform Installation</p>
            </div>

            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 33%"></div>
            </div>

            <!-- Step 1: Requirements Check -->
            <div class="step active" id="step1">
                <h2>System Requirements</h2>
                <div class="requirements">
                    <?php
                    $requirements = [
                        'PHP Version >= 8.1' => version_compare(PHP_VERSION, '8.1.0', '>='),
                        'PDO Extension' => extension_loaded('pdo'),
                        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
                        'JSON Extension' => extension_loaded('json'),
                        'mbstring Extension' => extension_loaded('mbstring'),
                        'OpenSSL Extension' => extension_loaded('openssl'),
                        'Storage Directory Writable' => is_writable(__DIR__ . '/storage') || mkdir(__DIR__ . '/storage', 0755, true),
                        'Public Directory Writable' => is_writable(__DIR__ . '/public'),
                    ];

                    $allOk = true;
                    foreach ($requirements as $req => $status) {
                        $allOk = $allOk && $status;
                        echo '<div class="req-item">';
                        echo '<div class="req-status ' . ($status ? 'req-ok' : 'req-error') . '">';
                        echo $status ? '✓' : '✗';
                        echo '</div>';
                        echo '<span>' . $req . '</span>';
                        echo '</div>';
                    }
                    ?>
                </div>
                <?php if ($allOk): ?>
                    <button class="btn" onclick="nextStep()">Continue to Database Setup</button>
                <?php else: ?>
                    <div class="alert alert-error">
                        Please fix the requirements above before continuing.
                    </div>
                <?php endif; ?>
            </div>

            <!-- Step 2: Database Configuration -->
            <div class="step" id="step2">
                <h2>Database Configuration</h2>
                <form id="dbForm">
                    <div class="form-group">
                        <label for="db_host">Database Host</label>
                        <input type="text" id="db_host" name="db_host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label for="db_port">Database Port</label>
                        <input type="text" id="db_port" name="db_port" value="3306" required>
                    </div>
                    <div class="form-group">
                        <label for="db_name">Database Name</label>
                        <input type="text" id="db_name" name="db_name" value="astrogenix" required>
                    </div>
                    <div class="form-group">
                        <label for="db_user">Database Username</label>
                        <input type="text" id="db_user" name="db_user" required>
                    </div>
                    <div class="form-group">
                        <label for="db_pass">Database Password</label>
                        <input type="password" id="db_pass" name="db_pass">
                    </div>
                    <button type="button" class="btn" onclick="testConnection()">Test Connection & Install</button>
                </form>
            </div>

            <!-- Step 3: Installation Complete -->
            <div class="step" id="step3">
                <h2>Installation Complete!</h2>
                <div class="alert alert-success">
                    <strong>Congratulations!</strong> AstroGenix has been successfully installed.
                </div>
                <p><strong>Default Admin Credentials:</strong></p>
                <p>Email: <code><EMAIL></code></p>
                <p>Password: <code>admin123</code></p>
                <p><strong>⚠️ Important:</strong> Please change the admin password after first login!</p>
                <a href="/login" class="btn">Go to Login Page</a>
            </div>
        </div>

        <script>
            let currentStep = 1;

            function nextStep() {
                if (currentStep < 3) {
                    document.getElementById('step' + currentStep).classList.remove('active');
                    currentStep++;
                    document.getElementById('step' + currentStep).classList.add('active');
                    updateProgress();
                }
            }

            function updateProgress() {
                const progress = (currentStep / 3) * 100;
                document.getElementById('progressBar').style.width = progress + '%';
            }

            function testConnection() {
                const formData = new FormData(document.getElementById('dbForm'));
                const btn = event.target;
                btn.disabled = true;
                btn.textContent = 'Installing...';

                fetch('install.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        nextStep();
                    } else {
                        alert('Installation failed: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Installation failed: ' + error.message);
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.textContent = 'Test Connection & Install';
                });
            }
        </script>
    </body>
    </html>
    <?php
    exit;
}

// Обработка POST запроса для установки
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        // Получаем данные формы
        $dbHost = $_POST['db_host'] ?? 'localhost';
        $dbPort = $_POST['db_port'] ?? '3306';
        $dbName = $_POST['db_name'] ?? 'astrogenix';
        $dbUser = $_POST['db_user'] ?? '';
        $dbPass = $_POST['db_pass'] ?? '';
        
        if (empty($dbUser)) {
            throw new Exception('Database username is required');
        }
        
        // Создаем .env файл
        $envContent = "# AstroGenix Environment Configuration\n\n";
        $envContent .= "# Application\n";
        $envContent .= "APP_ENV=production\n";
        $envContent .= "APP_DEBUG=false\n";
        $envContent .= "APP_KEY=" . base64_encode(random_bytes(32)) . "\n\n";
        $envContent .= "# Database\n";
        $envContent .= "DB_HOST={$dbHost}\n";
        $envContent .= "DB_PORT={$dbPort}\n";
        $envContent .= "DB_DATABASE={$dbName}\n";
        $envContent .= "DB_USERNAME={$dbUser}\n";
        $envContent .= "DB_PASSWORD={$dbPass}\n\n";
        $envContent .= "# Security\n";
        $envContent .= "SESSION_LIFETIME=120\n";
        $envContent .= "CSRF_TOKEN_LIFETIME=3600\n\n";
        $envContent .= "# Investment Settings\n";
        $envContent .= "WELCOME_BONUS=10.0\n";
        $envContent .= "MIN_WITHDRAWAL=5.0\n";
        $envContent .= "WITHDRAWAL_FEE=2.0\n";
        
        file_put_contents(__DIR__ . '/.env', $envContent);
        
        // Тестируем подключение к базе данных
        $dsn = "mysql:host={$dbHost};port={$dbPort};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbUser, $dbPass, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Создаем базу данных если её нет
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$dbName}`");
        
        // Запускаем инициализацию базы данных
        require_once __DIR__ . '/database/init.php';
        
        echo json_encode(['success' => true, 'message' => 'Installation completed successfully']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}
?>
