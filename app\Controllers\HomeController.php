<?php

namespace App\Controllers;

use App\Models\User;
use App\Models\UserBalance;
use App\Models\News;

/**
 * Контроллер главной страницы
 */
class HomeController extends BaseController
{
    /**
     * Главная страница
     */
    public function index(): void
    {
        // Если пользователь авторизован, перенаправляем в панель управления
        if ($this->session->isLoggedIn()) {
            $this->redirect('/dashboard');
        }

        // Получаем статистику платформы
        $stats = $this->getPlatformStats();
        
        // Получаем последние новости
        $newsModel = new News();
        $latestNews = $newsModel->getLatestNews(3);
        
        // Получаем планы инвестиций
        $investmentPlans = $this->config['investment']['plans'];
        
        $this->render('home.index', [
            'stats' => $stats,
            'news' => $latestNews,
            'investmentPlans' => $investmentPlans,
            'welcomeBonus' => $this->config['investment']['welcome_bonus']
        ]);
    }

    /**
     * Получение статистики платформы
     */
    private function getPlatformStats(): array
    {
        $userModel = new User();
        $balanceModel = new UserBalance();
        
        try {
            // Общее количество пользователей
            $totalUsers = $userModel->count(['status' => 'active']);
            
            // Общая сумма инвестиций
            $totalInvested = $this->getTotalInvested();
            
            // Общая сумма выплат
            $totalPaid = $this->getTotalPaid();
            
            // Активные инвестиции
            $activeInvestments = $this->getActiveInvestments();
            
            return [
                'total_users' => $totalUsers,
                'total_invested' => $totalInvested,
                'total_paid' => $totalPaid,
                'active_investments' => $activeInvestments,
                'online_users' => $this->getOnlineUsers()
            ];
            
        } catch (\Exception $e) {
            // В случае ошибки возвращаем демо-данные
            return [
                'total_users' => 1250,
                'total_invested' => 125000.50,
                'total_paid' => 45000.25,
                'active_investments' => 850,
                'online_users' => 45
            ];
        }
    }

    /**
     * Получение общей суммы инвестиций
     */
    private function getTotalInvested(): float
    {
        $userModel = new User();
        $result = $userModel->query(
            "SELECT COALESCE(SUM(amount), 0) as total FROM user_investments WHERE status = 'active'"
        );
        
        return (float) ($result[0]['total'] ?? 0);
    }

    /**
     * Получение общей суммы выплат
     */
    private function getTotalPaid(): float
    {
        $userModel = new User();
        $result = $userModel->query(
            "SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE type = 'profit' AND status = 'completed'"
        );
        
        return (float) ($result[0]['total'] ?? 0);
    }

    /**
     * Получение количества активных инвестиций
     */
    private function getActiveInvestments(): int
    {
        $userModel = new User();
        $result = $userModel->query(
            "SELECT COUNT(*) as count FROM user_investments WHERE status = 'active'"
        );
        
        return (int) ($result[0]['count'] ?? 0);
    }

    /**
     * Получение количества онлайн пользователей
     */
    private function getOnlineUsers(): int
    {
        $userModel = new User();
        $result = $userModel->query(
            "SELECT COUNT(*) as count FROM users 
             WHERE status = 'active' 
             AND last_activity_at >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)"
        );
        
        return (int) ($result[0]['count'] ?? 0);
    }
}
