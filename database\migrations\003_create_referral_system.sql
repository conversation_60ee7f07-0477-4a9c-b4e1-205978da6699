-- Создание таблицы реферальных бонусов
CREATE TABLE referral_earnings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id INT NOT NULL,
    referred_id INT NOT NULL,
    level INT NOT NULL,
    amount DECIMAL(15,8) NOT NULL,
    percentage DECIMAL(5,2) NOT NULL,
    source_type ENUM('investment', 'profit') NOT NULL,
    source_amount DECIMAL(15,8) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_referrer_id (referrer_id),
    INDEX idx_referred_id (referred_id),
    INDEX idx_level (level),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (referred_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Создание таблицы статистики рефералов
CREATE TABLE referral_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    level INT NOT NULL,
    total_referrals INT DEFAULT 0,
    active_referrals INT DEFAULT 0,
    total_earnings DECIMAL(15,8) DEFAULT 0.********,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_level (user_id, level),
    INDEX idx_user_id (user_id),
    INDEX idx_level (level),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Создание таблицы заданий (квестов)
CREATE TABLE quests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type ENUM('daily_login', 'weekly_activity', 'referral_count', 'first_deposit', 'social_follow') NOT NULL,
    target_value INT DEFAULT 1,
    reward_amount DECIMAL(15,8) NOT NULL,
    reward_type ENUM('main', 'bonus') DEFAULT 'bonus',
    is_repeatable BOOLEAN DEFAULT FALSE,
    reset_period ENUM('daily', 'weekly', 'monthly', 'never') DEFAULT 'never',
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
);

-- Создание таблицы выполненных заданий
CREATE TABLE user_quests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    quest_id INT NOT NULL,
    progress INT DEFAULT 0,
    target_value INT NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    last_progress_at TIMESTAMP NULL,
    reset_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_quest (user_id, quest_id),
    INDEX idx_user_id (user_id),
    INDEX idx_quest_id (quest_id),
    INDEX idx_is_completed (is_completed),
    INDEX idx_completed_at (completed_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (quest_id) REFERENCES quests(id) ON DELETE CASCADE
);

-- Вставка базовых заданий
INSERT INTO quests (name, description, type, target_value, reward_amount, reward_type, is_repeatable, reset_period, sort_order) VALUES
('Daily Login', 'Login to your account daily', 'daily_login', 1, 0.********, 'bonus', TRUE, 'daily', 1),
('Weekly Activity', 'Be active for 7 consecutive days', 'weekly_activity', 7, 5.********, 'bonus', TRUE, 'weekly', 2),
('First Referral', 'Invite your first referral', 'referral_count', 1, 10.********, 'bonus', FALSE, 'never', 3),
('3 Referrals', 'Invite 3 referrals', 'referral_count', 3, 25.********, 'bonus', FALSE, 'never', 4),
('5 Referrals', 'Invite 5 referrals', 'referral_count', 5, 50.********, 'bonus', FALSE, 'never', 5),
('10 Referrals', 'Invite 10 referrals', 'referral_count', 10, 100.********, 'bonus', FALSE, 'never', 6),
('First Deposit', 'Make your first deposit', 'first_deposit', 1, 15.********, 'bonus', FALSE, 'never', 7),
('Follow Telegram', 'Follow our Telegram channel', 'social_follow', 1, 2.********, 'bonus', FALSE, 'never', 8),
('Follow Twitter', 'Follow our Twitter account', 'social_follow', 1, 2.********, 'bonus', FALSE, 'never', 9),
('Subscribe YouTube', 'Subscribe to our YouTube channel', 'social_follow', 1, 3.********, 'bonus', FALSE, 'never', 10);
