<?php

namespace App\Models;

/**
 * Модель пользователя
 */
class User extends BaseModel
{
    protected string $table = 'users';
    
    protected array $fillable = [
        'email', 'password', 'first_name', 'last_name', 'phone', 'country',
        'referrer_id', 'referral_code', 'role', 'status', 'email_verified_at',
        'last_login_at', 'last_activity_at'
    ];
    
    protected array $hidden = ['password'];
    
    protected array $casts = [
        'id' => 'int',
        'referrer_id' => 'int',
        'email_verified_at' => 'string',
        'last_login_at' => 'string',
        'last_activity_at' => 'string'
    ];

    /**
     * Поиск пользователя по email
     */
    public function findByEmail(string $email): ?array
    {
        return $this->findBy(['email' => $email]);
    }

    /**
     * Поиск пользователя по реферальному коду
     */
    public function findByReferralCode(string $code): ?array
    {
        return $this->findBy(['referral_code' => $code]);
    }

    /**
     * Создание пользователя с хешированием пароля
     */
    public function createUser(array $data): int
    {
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_ARGON2ID);
        }
        
        if (!isset($data['referral_code'])) {
            $data['referral_code'] = $this->generateReferralCode();
        }
        
        return $this->create($data);
    }

    /**
     * Проверка пароля
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Обновление времени последнего входа
     */
    public function updateLastLogin(int $userId): bool
    {
        return $this->update($userId, [
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_activity_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Обновление времени последней активности
     */
    public function updateLastActivity(int $userId): bool
    {
        return $this->update($userId, [
            'last_activity_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Получение рефералов пользователя
     */
    public function getReferrals(int $userId, int $level = 1): array
    {
        if ($level === 1) {
            return $this->all(['referrer_id' => $userId], 'created_at DESC');
        }
        
        // Для многоуровневых рефералов используем рекурсивный запрос
        $sql = "
            WITH RECURSIVE referral_tree AS (
                SELECT id, email, first_name, last_name, referrer_id, created_at, 1 as level
                FROM users 
                WHERE referrer_id = :user_id
                
                UNION ALL
                
                SELECT u.id, u.email, u.first_name, u.last_name, u.referrer_id, u.created_at, rt.level + 1
                FROM users u
                INNER JOIN referral_tree rt ON u.referrer_id = rt.id
                WHERE rt.level < :max_level
            )
            SELECT * FROM referral_tree ORDER BY level, created_at DESC
        ";
        
        return $this->query($sql, ['user_id' => $userId, 'max_level' => $level]);
    }

    /**
     * Получение статистики рефералов
     */
    public function getReferralStats(int $userId): array
    {
        $sql = "
            SELECT 
                COUNT(CASE WHEN level = 1 THEN 1 END) as level_1_count,
                COUNT(CASE WHEN level = 2 THEN 1 END) as level_2_count,
                COUNT(CASE WHEN level = 3 THEN 1 END) as level_3_count,
                COUNT(*) as total_referrals
            FROM (
                WITH RECURSIVE referral_tree AS (
                    SELECT id, referrer_id, 1 as level
                    FROM users 
                    WHERE referrer_id = :user_id
                    
                    UNION ALL
                    
                    SELECT u.id, u.referrer_id, rt.level + 1
                    FROM users u
                    INNER JOIN referral_tree rt ON u.referrer_id = rt.id
                    WHERE rt.level < 3
                )
                SELECT * FROM referral_tree
            ) as referrals
        ";
        
        $result = $this->db->fetch($sql, ['user_id' => $userId]);
        
        return [
            'level_1' => (int) $result['level_1_count'],
            'level_2' => (int) $result['level_2_count'],
            'level_3' => (int) $result['level_3_count'],
            'total' => (int) $result['total_referrals']
        ];
    }

    /**
     * Генерация уникального реферального кода
     */
    private function generateReferralCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
        } while ($this->findByReferralCode($code));
        
        return $code;
    }

    /**
     * Получение топ пользователей по рефералам
     */
    public function getTopReferrers(int $limit = 10): array
    {
        $sql = "
            SELECT u.id, u.first_name, u.last_name, u.email, 
                   COUNT(r.id) as referral_count,
                   u.created_at
            FROM users u
            LEFT JOIN users r ON r.referrer_id = u.id
            WHERE u.status = 'active'
            GROUP BY u.id
            HAVING referral_count > 0
            ORDER BY referral_count DESC, u.created_at ASC
            LIMIT :limit
        ";
        
        return $this->query($sql, ['limit' => $limit]);
    }

    /**
     * Получение активных пользователей
     */
    public function getActiveUsers(int $days = 30): array
    {
        $sql = "
            SELECT * FROM users 
            WHERE status = 'active' 
            AND last_activity_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            ORDER BY last_activity_at DESC
        ";
        
        return $this->query($sql, ['days' => $days]);
    }

    /**
     * Поиск пользователей
     */
    public function search(string $query, int $limit = 20): array
    {
        $sql = "
            SELECT id, email, first_name, last_name, status, created_at
            FROM users 
            WHERE (email LIKE :query 
                   OR first_name LIKE :query 
                   OR last_name LIKE :query
                   OR CONCAT(first_name, ' ', last_name) LIKE :query)
            ORDER BY created_at DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, [
            'query' => "%{$query}%",
            'limit' => $limit
        ]);
    }
}
