<!DOCTYPE html>
<html lang="<?= $locale ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= $csrfToken ?>">
    <title><?= $config['app']['name'] ?> - Premium USDT Staking Platform</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="/assets/css/app.css" rel="stylesheet">
    <link href="/assets/css/components.css" rel="stylesheet">
    
    <!-- Meta Tags -->
    <meta name="description" content="AstroGenix - Premium USDT Staking Platform with daily profits up to 2.5%">
    <meta name="keywords" content="USDT, staking, investment, crypto, mining, profit">
    <meta name="author" content="AstroGenix">
    
    <!-- Open Graph -->
    <meta property="og:title" content="AstroGenix - Premium USDT Staking Platform">
    <meta property="og:description" content="Earn daily profits up to 2.5% with our premium USDT staking platform">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $_SERVER['REQUEST_URI'] ?>">
    <meta property="og:image" content="/assets/images/og-image.jpg">
</head>
<body class="<?= $currentUser ? 'authenticated' : 'guest' ?>">
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <img src="/assets/images/logo.svg" alt="AstroGenix">
            </div>
            <div class="loading-spinner"></div>
            <div class="loading-text"><?= $t('common.loading') ?></div>
        </div>
    </div>

    <!-- Navigation -->
    <?php if ($currentUser): ?>
        <?php include 'partials/navbar-authenticated.php'; ?>
    <?php else: ?>
        <?php include 'partials/navbar-guest.php'; ?>
    <?php endif; ?>

    <!-- Flash Messages -->
    <?php if (!empty($flashMessages)): ?>
        <div class="flash-messages">
            <?php foreach ($flashMessages as $type => $message): ?>
                <div class="alert alert-<?= $type ?>" data-auto-dismiss="5000">
                    <div class="alert-content">
                        <span class="alert-icon">
                            <?php if ($type === 'success'): ?>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            <?php elseif ($type === 'error'): ?>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                            <?php else: ?>
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            <?php endif; ?>
                        </span>
                        <span class="alert-message"><?= htmlspecialchars($message) ?></span>
                    </div>
                    <button class="alert-close" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content">
        <?php include $viewPath; ?>
    </main>

    <!-- Footer -->
    <?php include 'partials/footer.php'; ?>

    <!-- Modals -->
    <div id="modal-container"></div>

    <!-- Scripts -->
    <script src="/assets/js/app.js"></script>
    <script src="/assets/js/components.js"></script>
    
    <!-- Localization -->
    <script>
        window.locale = '<?= $locale ?>';
        window.translations = <?= json_encode($t) ?>;
        window.csrfToken = '<?= $csrfToken ?>';
        window.currentUser = <?= $currentUser ? json_encode([
            'id' => $currentUser['id'],
            'email' => $currentUser['email'],
            'first_name' => $currentUser['first_name'],
            'last_name' => $currentUser['last_name'],
            'role' => $currentUser['role']
        ]) : 'null' ?>;
    </script>

    <!-- Additional Scripts -->
    <?php if (isset($additionalScripts)): ?>
        <?php foreach ($additionalScripts as $script): ?>
            <script src="<?= $script ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline Scripts -->
    <?php if (isset($inlineScripts)): ?>
        <script>
            <?= $inlineScripts ?>
        </script>
    <?php endif; ?>

    <!-- Analytics (if needed) -->
    <?php if (isset($config['analytics']['google_analytics_id'])): ?>
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?= $config['analytics']['google_analytics_id'] ?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '<?= $config['analytics']['google_analytics_id'] ?>');
        </script>
    <?php endif; ?>
</body>
</html>
