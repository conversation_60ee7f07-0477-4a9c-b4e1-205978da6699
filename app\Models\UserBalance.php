<?php

namespace App\Models;

/**
 * Модель балансов пользователей
 */
class UserBalance extends BaseModel
{
    protected string $table = 'user_balances';
    
    protected array $fillable = [
        'user_id', 'balance_type', 'amount'
    ];
    
    protected array $casts = [
        'user_id' => 'int',
        'amount' => 'float'
    ];

    /**
     * Создание балансов для нового пользователя
     */
    public function createUserBalances(int $userId): void
    {
        $balanceTypes = ['main', 'bonus', 'referral'];
        
        foreach ($balanceTypes as $type) {
            $this->create([
                'user_id' => $userId,
                'balance_type' => $type,
                'amount' => 0.00000000
            ]);
        }
    }

    /**
     * Получение баланса пользователя по типу
     */
    public function getUserBalance(int $userId, string $balanceType = 'main'): float
    {
        $balance = $this->findBy([
            'user_id' => $userId,
            'balance_type' => $balanceType
        ]);
        
        return $balance ? (float) $balance['amount'] : 0.0;
    }

    /**
     * Получение всех балансов пользователя
     */
    public function getAllUserBalances(int $userId): array
    {
        $balances = $this->all(['user_id' => $userId]);
        
        $result = [
            'main' => 0.0,
            'bonus' => 0.0,
            'referral' => 0.0,
            'total' => 0.0
        ];
        
        foreach ($balances as $balance) {
            $result[$balance['balance_type']] = (float) $balance['amount'];
        }
        
        $result['total'] = $result['main'] + $result['bonus'] + $result['referral'];
        
        return $result;
    }

    /**
     * Добавление средств к балансу
     */
    public function addBalance(int $userId, float $amount, string $balanceType = 'main'): bool
    {
        if ($amount <= 0) {
            return false;
        }
        
        $balance = $this->findBy([
            'user_id' => $userId,
            'balance_type' => $balanceType
        ]);
        
        if (!$balance) {
            // Создаем баланс если его нет
            $this->create([
                'user_id' => $userId,
                'balance_type' => $balanceType,
                'amount' => $amount
            ]);
            return true;
        }
        
        $newAmount = (float) $balance['amount'] + $amount;
        
        return $this->update($balance['id'], ['amount' => $newAmount]);
    }

    /**
     * Списание средств с баланса
     */
    public function subtractBalance(int $userId, float $amount, string $balanceType = 'main'): bool
    {
        if ($amount <= 0) {
            return false;
        }
        
        $balance = $this->findBy([
            'user_id' => $userId,
            'balance_type' => $balanceType
        ]);
        
        if (!$balance || (float) $balance['amount'] < $amount) {
            return false; // Недостаточно средств
        }
        
        $newAmount = (float) $balance['amount'] - $amount;
        
        return $this->update($balance['id'], ['amount' => $newAmount]);
    }

    /**
     * Перевод средств между балансами
     */
    public function transferBalance(int $userId, float $amount, string $fromType, string $toType): bool
    {
        if ($amount <= 0 || $fromType === $toType) {
            return false;
        }
        
        $this->db->beginTransaction();
        
        try {
            // Списываем с исходного баланса
            if (!$this->subtractBalance($userId, $amount, $fromType)) {
                throw new \Exception('Insufficient funds');
            }
            
            // Добавляем к целевому балансу
            if (!$this->addBalance($userId, $amount, $toType)) {
                throw new \Exception('Failed to add to target balance');
            }
            
            $this->db->commit();
            return true;
            
        } catch (\Exception $e) {
            $this->db->rollback();
            return false;
        }
    }

    /**
     * Получение истории изменений баланса
     */
    public function getBalanceHistory(int $userId, int $limit = 50): array
    {
        $sql = "
            SELECT t.*, 
                   CASE 
                       WHEN t.type IN ('deposit', 'investment', 'profit', 'referral_bonus', 'quest_reward', 'welcome_bonus') 
                       THEN 'credit' 
                       ELSE 'debit' 
                   END as transaction_type
            FROM transactions t
            WHERE t.user_id = :user_id
            ORDER BY t.created_at DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, ['user_id' => $userId, 'limit' => $limit]);
    }

    /**
     * Получение статистики баланса
     */
    public function getBalanceStats(int $userId): array
    {
        $sql = "
            SELECT 
                SUM(CASE WHEN type IN ('deposit', 'welcome_bonus') THEN amount ELSE 0 END) as total_deposited,
                SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawn,
                SUM(CASE WHEN type = 'profit' THEN amount ELSE 0 END) as total_profit,
                SUM(CASE WHEN type = 'referral_bonus' THEN amount ELSE 0 END) as total_referral_earnings,
                SUM(CASE WHEN type = 'quest_reward' THEN amount ELSE 0 END) as total_quest_rewards,
                SUM(CASE WHEN type = 'investment' THEN amount ELSE 0 END) as total_invested
            FROM transactions
            WHERE user_id = :user_id AND status = 'completed'
        ";
        
        $result = $this->db->fetch($sql, ['user_id' => $userId]);
        
        return [
            'total_deposited' => (float) ($result['total_deposited'] ?? 0),
            'total_withdrawn' => (float) ($result['total_withdrawn'] ?? 0),
            'total_profit' => (float) ($result['total_profit'] ?? 0),
            'total_referral_earnings' => (float) ($result['total_referral_earnings'] ?? 0),
            'total_quest_rewards' => (float) ($result['total_quest_rewards'] ?? 0),
            'total_invested' => (float) ($result['total_invested'] ?? 0)
        ];
    }

    /**
     * Проверка возможности вывода средств
     */
    public function canWithdraw(int $userId, float $amount): array
    {
        $balances = $this->getAllUserBalances($userId);
        $stats = $this->getBalanceStats($userId);
        
        $result = [
            'can_withdraw' => false,
            'reason' => '',
            'available_amount' => 0.0
        ];
        
        // Проверяем минимальную сумму
        $minWithdrawal = 5.0; // Из конфига
        if ($amount < $minWithdrawal) {
            $result['reason'] = "Minimum withdrawal amount is {$minWithdrawal} USDT";
            return $result;
        }
        
        // Проверяем доступные средства
        $availableForWithdrawal = $balances['main'];
        
        // Если пользователь не делал депозитов, может вывести только часть бонусных средств
        if ($stats['total_deposited'] == 0) {
            $maxBonusWithdrawal = $balances['bonus'] * 0.1; // 10% от бонусного баланса
            $availableForWithdrawal = min($balances['main'], $maxBonusWithdrawal);
        }
        
        $result['available_amount'] = $availableForWithdrawal;
        
        if ($amount > $availableForWithdrawal) {
            if ($stats['total_deposited'] == 0) {
                $result['reason'] = 'Make a deposit to unlock full withdrawal access';
            } else {
                $result['reason'] = 'Insufficient funds';
            }
            return $result;
        }
        
        $result['can_withdraw'] = true;
        return $result;
    }

    /**
     * Получение топ пользователей по балансу
     */
    public function getTopBalances(int $limit = 10): array
    {
        $sql = "
            SELECT u.id, u.first_name, u.last_name, u.email,
                   SUM(ub.amount) as total_balance,
                   u.created_at
            FROM users u
            JOIN user_balances ub ON u.id = ub.user_id
            WHERE u.status = 'active'
            GROUP BY u.id
            ORDER BY total_balance DESC
            LIMIT :limit
        ";
        
        return $this->query($sql, ['limit' => $limit]);
    }
}
